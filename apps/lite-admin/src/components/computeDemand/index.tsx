import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/dataTable';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { fetchTeamHashrateLog } from "@/api/computeDemand";
import {
  TeamHashrateLogItem,
  TeamHashrateLogResponse,
} from '@/types/computeDemand';
import { formatDate } from '@mono/utils/day';
import { Input } from '@mono/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@mono/ui/select';
import { Button } from '@mono/ui/button';
import { DateRange } from 'react-day-picker';
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { ScrollArea } from '@mono/ui/scroll-area';

export function TeamHashrateLogIndex() {
  const [searchParams, setSearchParams] = useState({
    page: 1,
    size: 10,
    searchType: 1, 
    searchText: '',
    startDate: undefined as number | undefined,
    endDate: undefined as number | undefined
  });


  const [formState, setFormState] = useState({
    searchType: 1,
    searchText: '',
    dateRange: undefined as DateRange | undefined
  });

  const { data: teamData } = useQuery<TeamHashrateLogResponse>({
    queryKey: ['fetchTeamHashrateLog', searchParams],
    queryFn: () => fetchTeamHashrateLog({
      page: searchParams.page,
      size: searchParams.size,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      startDate: searchParams.startDate?.toString() || '',
      endDate: searchParams.endDate?.toString() || ''
    }),
  });

  const handleSearchTypeChange = (value: string) => {
    setFormState(prev => ({
      ...prev,
      searchType: Number(value),
      searchText: ''
    }));
  };

  const handleSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormState(prev => ({
      ...prev,
      searchText: e.target.value
    }));
  };

  const handleDateChange = (range: DateRange | undefined) => {
    setFormState(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  const handleSearchSubmit = () => {
    setSearchParams({
      page: 1,
      size: searchParams.size,
      searchType: formState.searchType,
      searchText: formState.searchText,
      startDate: formState.dateRange?.from?.getTime(),
      endDate: formState.dateRange?.to?.getTime()
    });
  };

  const handleReset = () => {
    setFormState({
      searchType: 1,
      searchText: '',
      dateRange: undefined
    });

    setSearchParams({
      page: 1,
      size: 10,
      searchType: 1,
      searchText: '',
      startDate: undefined,
      endDate: undefined
    });
  };

  const handlePaginationChange = (page: number, size: number) => {
    setSearchParams(prev => ({
      ...prev,
      page,
      size
    }));
  };


  const ChangeSwitch = (changeType: number): string => {
    const mapping: Record<number, string> = {
      104: '生成口播文案',
      105: '小红书',
      106: '文案修改',
      107: '生成标题',
      108: '微信朋友圈',
      109: '翻译',
      110: '训练数字人',

      111: '过期',
      112: '训练声音',
      113: '生成数字人视频',
      114: '生成数字人',
      115: '充值储存额度',
      116: '声音试听',
      117: '算力订单退款',

      200: '退款',
      201: '赠送极速算力',
      202: '用户充值',
      203: '数字人训练失败退回',
      204: '训练声音退回',
      205: '生成视频剩余算力退回',
      206: '声音试听失败退回',
      207: '兑换码',
    };

    return mapping[changeType] || '--';
  };


  const columns: ColumnDef<TeamHashrateLogItem, unknown>[] = [
    {
      header: '流水号',
      accessorKey: '_id',
      cell: ({ row }) => <span className="text-blue-500">{row.original._id}</span>,
    },
    {
      header: '变更时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => (
        <span>
          {formatDate(new Date(row.original.createdAt).getTime())}
        </span>
      ),
    },
    {
      header: '团队编号',
      accessorKey: 'teamCode',
    },
    {
      header: '团队名称',
      accessorKey: 'teamName',
    },
    {
      header: '用户信息',
      accessorKey: '',
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span>{row.original.userNickName}</span>
          <span className="text-gray-500 text-sm">{row.original.userPhone}</span>
        </div>
      ),
    },
    {
      header: '变更类型',
      accessorKey: 'changeType',
      cell: ({ row }) => {
        return <span>{ChangeSwitch(row.original.changeType)}</span>;
      },
    },
    {
      header: '算力变更',
      accessorKey: 'changeValue',
      cell: ({ row }) => {
        const value = Number(row.original.changeValue);
        const isPositive = value >= 0;
        const displayValue = isPositive ? `+${value}` : value.toString();

        return (
          <span className={isPositive ? 'text-red-500' : 'text-green-500'}>
            {displayValue}
          </span>
        );
      },
    },
    {
      header: '变更前后',
      accessorKey: '',
      cell: ({ row }) => (
        <div className="flex flex-col">
          <span>前: {row.original.beforeValue}</span>
          <span>后: {row.original.afterValue}</span>
        </div>
      ),
    },
    {
      header: '关联订单',
      accessorKey: 'orderSns',
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1 max-w-[150px]">
          {row.original.orderSns?.map(sn => (
            <span key={sn} className="px-1 bg-gray-100 rounded text-xs">{sn}</span>
          ))}
        </div>
      ),
    },
    {
      header: '描述',
      accessorKey: 'description',
      cell: ({ row }) => <span className="text-gray-600">{row.original.description}</span>,
    },
  ];

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="p-4">
        <h1 className="text-xl font-bold mb-4">团队算力变更记录</h1>

        <div className="flex flex-wrap gap-4 mb-4 items-end">
          <div className="min-w-[200px] flex">
            <label className="block text-sm font-medium mb-1 w-[60%] pt-2">搜索类型</label>
            <Select
              value={String(formState.searchType)}
              onValueChange={handleSearchTypeChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="选择搜索类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">团队编号</SelectItem>
                <SelectItem value="2">流水号</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex min-w-[200px]">
            <label className="block text-sm font-medium mb-1 w-[60%] pt-2">搜索内容</label>
            <Input
              placeholder={`输入${formState.searchType === 1 ? '团队编号' : '流水号'}`}
              value={formState.searchText}
              onChange={handleSearchTextChange}
            />
          </div>

          <div className="flex min-w-[250px]">
            <label className="block text-sm font-medium mb-1 w-[60%] pt-2 mr-3">时间范围</label>
            <DatePickerWithRange
              onChange={handleDateChange}
              value={formState.dateRange}
              className="w-full"
            />
          </div>

          <Button className="h-[40px]" variant="outline" onClick={handleReset}>重置</Button>
          <Button className="h-[40px]" onClick={handleSearchSubmit}>搜索</Button>
        </div>

        {/* 数据表格 */}
        <DataTable
          columns={columns}
          data={teamData?.data || []}
          rowCount={teamData?.totalSize || 0}
          pagination={{
            pageIndex: searchParams.page - 1,
            pageSize: searchParams.size,
          }}
          setPagination={(updater) => {
            const newPagination = typeof updater === 'function'
              ? updater({
                pageIndex: searchParams.page - 1,
                pageSize: searchParams.size
              })
              : updater;
            handlePaginationChange(newPagination.pageIndex + 1, newPagination.pageSize);
          }}
        />
      </div>
    </ScrollArea>
  );
}

export const ComputeDemandIndex = TeamHashrateLogIndex;