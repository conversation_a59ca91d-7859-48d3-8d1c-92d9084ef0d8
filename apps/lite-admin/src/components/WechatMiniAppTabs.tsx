import { useState, useRef, useEffect, } from "react";
// import { useState, useRef,useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@mono/ui/tabs";
import { Card } from "@mono/ui/card";
import { Button } from "@mono/ui/button";
import { Play, ChevronLeft, ChevronRight } from "lucide-react";


type SoundItem = {
  typeName: string;
  content: string;
  voiceUrl?: string;
  voiceSort?: number;
  _id: string;
};

type SoundData = SoundItem[]

interface WechatMiniAppTabsProps {
  soundData: SoundData;
}

export function WechatMiniAppTabs({ soundData }: WechatMiniAppTabsProps) {
  const tabsRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<string | undefined>(undefined);

  const safeSoundData = Array.isArray(soundData) ? soundData : [];



  const tabItems = safeSoundData.map((item, index) => ({
    ...item,
    // 创建唯一ID:typeName+index+_id
    uniqueTabId: `${item.typeName}-${index}-${item._id}`
  }));

  // 默认activeTab，变化时指向第一个
  useEffect(() => {
    if (tabItems.length > 0) {
      setActiveTab(tabItems[0].uniqueTabId);
    }
  }, [soundData]);


  const scrollTabs = (direction: "left" | "right") => {
    if (tabsRef.current) {
      const scrollAmount = direction === "left" ? -500 : 500;
      tabsRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    }
  };

  return (
    <div className="w-full mx-auto bg-black text-white p-6 pt-4 rounded-lg">
      <div className="relative">
        {/* 左侧滚动按钮 */}
        <button
          onClick={() => scrollTabs("left")}
          className="absolute left-[-24px] top-1/2 -translate-y-1/2 z-10 bg-black/80 hover:bg-black py-0 "
        >
          <ChevronLeft className="h-5 w-5" />
        </button>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-[287px]"
        >
          <TabsList
            ref={tabsRef}
            className="
            flex w-full bg-black border-b 
            border-gray-700 rounded-none  
            overflow-x-auto no-scroll  p-0 
            py-0 justify-start"
          >
            {/* 导航标签 */}
            {tabItems.map((tab) => (
              <TabsTrigger
                key={tab.uniqueTabId}
                value={tab.uniqueTabId}
                className="
                 text-[15px] 
                px-2 py-0 whitespace-nowrap 
                data-[state=active]:bg-transparent 
                data-[state=active]:text-green-500 
                data-[state=active]:border-b-2 
                data-[state=active]:border-green-500
                
                active:bg-white active:text-black
               focus-visible:bg-white focus-visible:text-black
               hover:bg-white hover:text-black
                rounded-[1px]
                transition-colors duration-500 ease-in-out
                "
              >
                {tab.typeName}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>

        {/* 右侧滚动按钮 */}
        <button
          onClick={() => scrollTabs("right")}
          className="absolute right-[-24px] top-1/2 -translate-y-1/2 z-10 bg-black/80 hover:bg-black py-2"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      {/* 内容区域 */}
      <Tabs value={activeTab} className="w-full">
        {tabItems.map((tab) => (
          <TabsContent key={tab.uniqueTabId} value={tab.uniqueTabId} >
            <Card key={tab._id} className="bg-black border-gray-800 p-4 text-white mb-4 ">
              <p className="mb-4 text-base leading-relaxed ">{tab.content}</p>
              <div className="flex items-center">
                {tab.voiceUrl ? (<Button variant="ghost" className="text-green-500 hover:bg-gray-800">
                  <Play className="mr-2 h-4 w-4" />
                  试听示例
                </Button>) : (<></>)}
              </div>
            </Card>
          </TabsContent>
        ))}
        <small className="text-m mt-2 block pl-0 leading-6 text-gray-500">
          <>
            1. 选择一个安静的环境，录制时长需至少 10 秒。<br />
            2. 参考文案仅作参考，无需逐字念，可自由发挥。<br />
            3. 语气和风格会被克隆，需要选择合适的参考文案风格。<br />
          </>
        </small>
      </Tabs>
      <style>
        {`
            .no-scroll::-webkit-scrollbar{
              display: none;
            }
          `}
      </style>
    </div>
  );
}