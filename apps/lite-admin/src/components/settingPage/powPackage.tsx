import { Button } from '@mono/ui/button';
import { Input } from '@mono/ui/input';
import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  fetchPowConsumList,
  addPowConsum,
  repairPowConsum,
  delPowConsum
} from '@/api/settingPage/powPackage';
import type {
  ProductBase,
  ProductBaseRequest,
} from '@/types/powPackage';
import { Dialog, DialogContent, DialogFooter, DialogHeader } from '@mono/ui/dialog';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Loader2 } from 'lucide-react';

interface PowerPackage extends ProductBaseRequest {
  appKey: string;
}

export function PowPackageIndex() {
  const queryClient = useQueryClient();
  const [isAdding, setIsAdding] = useState(false);
  const [newProduct, setNewProduct] = useState<ProductBase>({
    productName: '',
    hashrate: 0,
    price: 0
  });

  const { data: packages = [], isLoading, isError } = useQuery({
    queryKey: ['fetchPowConsumList'],
    queryFn: () => fetchPowConsumList({
      productName: '', 
      hashrate: 0,   
      price: 0       
    }),
  });

  const [editingRows, setEditingRows] = useState<Record<string, Partial<PowerPackage>>>({});

  const addMutation = useMutation({
    mutationFn: addPowConsum,
    onSuccess: () => {
      toast.success('产品添加成功');
      setIsAdding(false);
      setNewProduct({ productName: '', hashrate: 0, price: 0 });
      queryClient.invalidateQueries({ queryKey: ['fetchPowConsumList'] });
    },
    onError: (error) => {
      toast.error(`添加失败: ${error.message}`);
    }
  });

  const updateMutation = useMutation({
    mutationFn: repairPowConsum,
    onSuccess: () => {
      toast.success('产品更新成功');
      queryClient.invalidateQueries({ queryKey: ['fetchPowConsumList'] });
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: delPowConsum,
    onSuccess: () => {
      toast.success('产品删除成功');
      queryClient.invalidateQueries({ queryKey: ['fetchPowConsumList'] });
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  const startEditing = (_id: string) => {
    const pkg = packages.find(p => p._id === _id);
    if (pkg) {
      setEditingRows(prev => ({
        ...prev,
        [_id]: {
          productName: pkg.productName,
          hashrate: pkg.hashrate,
          price: pkg.price
        }
      }));
    }
  };

  const cancelEditing = (_id: string) => {
    setEditingRows(prev => {
      const newRows = { ...prev };
      delete newRows[_id];
      return newRows;
    });
  };

  const saveAllChanges = () => {
    Object.entries(editingRows).forEach(([_id, changes]) => {
      updateMutation.mutate({ _id, ...changes });
    });
    setEditingRows({});
  };

  const handleChange = (_id: string, field: keyof PowerPackage, value: string | number) => {
    setEditingRows(prev => ({
      ...prev,
      [_id]: {
        ...prev[_id],
        [field]: value
      }
    }));
  };

  const handleNewProductChange = (field: keyof ProductBase, value: string | number) => {
    setNewProduct(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddProduct = () => {
    if (!newProduct.productName || newProduct.hashrate <= 0 || newProduct.price <= 0) {
      toast.warning('请填写完整的产品信息');
      return;
    }
    addMutation.mutate(newProduct);
  };

  const formatDateTime = (isoString: string) => {
    if (!isoString) return '--';
    try {
      const date = new Date(isoString);
      return date.toISOString()
        .replace('T', ' ')
        .replace(/\.\d+Z$/, '');
    } catch (e) {
      return isoString; 
    }
  };

  return (
    <div className="flex flex-col gap-4 p-4 w-full max-w-6xl mx-auto">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-bold">算力包管理</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsAdding(true)}>新增产品</Button>

          <Button
            onClick={saveAllChanges}
            disabled={Object.keys(editingRows).length === 0 || updateMutation.isPending}
          >
            {updateMutation.isPending ? '保存中...' : '保存所有修改'}
          </Button>
        </div>
      </div>

      <Dialog open={isAdding} onOpenChange={setIsAdding}>
        <DialogContent>
          <DialogHeader className='flex justify-center items-center'>添加算力包产品</DialogHeader>

          <div className="p-4 border rounded-lg">
            <div className="grid  gap-4 mb-3 flex-col">
              <div className='flex'>
                <span className='w-[40%] mt-1.5 flex'>
                  <img src={RequiredIco} className='w-[14px] h-[14px]'/>
                  商品名称
                </span>
                <Input
                  placeholder="商品名称"
                  value={newProduct.productName}
                  onChange={(e) => handleNewProductChange('productName', e.target.value)}
                />
              </div>

              <div className='flex'>
                 <span className='w-[40%] mt-1.5 flex'>
                  <img src={RequiredIco} className='w-[14px] h-[14px]'/>
                  包含算力
                </span>
                <Input
                  type="number"
                  placeholder="包含算力"
                  value={newProduct.hashrate || ''}
                  onChange={(e) => handleNewProductChange('hashrate', parseInt(e.target.value) || 0)}
                />
              </div>

              <div className='flex'>
                <span className='w-[40%] mt-1.5 flex'>
                  <img src={RequiredIco} className='w-[14px] h-[14px]'/>
                  价格
                </span>
                <Input
                  type="number"
                  placeholder="价格"
                  value={newProduct.price || ''}
                  onChange={(e) => handleNewProductChange('price', parseInt(e.target.value) || 0)}
                />
              </div>



            </div>
          </div>


          <DialogFooter>
            <div className="flex gap-2">
              <Button
                onClick={handleAddProduct}
                disabled={addMutation.isPending}
              >
                {addMutation.isPending&&(
                  <Loader2 className='h-4 w-4 animate-spin'/>
                )}
                确认添加
              </Button>
              <Button variant="outline" onClick={() => setIsAdding(false)}>取消</Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {isLoading && <div className="text-center py-4">加载中...</div>}
      {isError && <div className="text-center py-4 text-red-500">加载失败，请重试</div>}
      {!isLoading && !isError && (
        <div className="border  overflow-hidden shadow-sm">
          <div className="grid grid-cols-12 bg-gray-100 p-3 border-b dark:bg-transparent">
            <div className="col-span-2 font-medium">产品名称</div>
            <div className="col-span-2 font-medium">算力</div>
            <div className="col-span-2 font-medium">价格</div>
            <div className="col-span-2 font-medium">创建时间</div>
            <div className="col-span-2 font-medium">更新时间</div>
            <div className="col-span-2 font-medium ml-8">操作</div>
          </div>

          {packages.map(pkg => (
            <div key={pkg._id} className="grid grid-cols-12 p-3 border-b hover:bg-gray-50 dark:hover:bg-black text-sm gap-2 items-center">
              <div className="col-span-2">
                {editingRows[pkg._id] ? (
                  <Input
                    value={editingRows[pkg._id].productName ?? pkg.productName}
                    onChange={(e) => handleChange(pkg._id, 'productName', e.target.value)}
                    className="w-full"
                  />
                ) : (
                  pkg.productName
                )}
              </div>

              <div className="col-span-2">
                {editingRows[pkg._id] ? (
                  <Input
                    type="number"
                    value={editingRows[pkg._id].hashrate ?? pkg.hashrate}
                    onChange={(e) => handleChange(pkg._id, 'hashrate', parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                ) : (
                  pkg.hashrate
                )}
              </div>

              <div className="col-span-2">
                {editingRows[pkg._id] ? (
                  <Input
                    type="number"
                    value={editingRows[pkg._id].price ?? pkg.price}
                    onChange={(e) => handleChange(pkg._id, 'price', parseInt(e.target.value) || 0)}
                    className="w-full"
                  />
                ) : (
                  pkg.price
                )}
              </div>

              <div className="col-span-2 text-sm text-gray-600">
                {formatDateTime(pkg.createdAt)}
              </div>

              <div className="col-span-2 text-sm text-gray-600">
                {formatDateTime(pkg.updatedAt)}
              </div>

              <div className="col-span-2 flex ml-8">
                {editingRows[pkg._id] ? (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => cancelEditing(pkg._id)}
                  >
                    取消
                  </Button>
                ) : (
                  <div className="flex gap-3">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => startEditing(pkg._id)}
                    >
                      编辑
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => deleteMutation.mutate({ _id: pkg._id })}
                      disabled={deleteMutation.isPending}
                    >
                      {deleteMutation.isPending ? '删除中...' : '删除'}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}