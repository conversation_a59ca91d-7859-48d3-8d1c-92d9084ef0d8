import { AppInfoResponse, fetchAppInfo, saveConfiguration } from "@/api/settingPage/appInfo";
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Switch } from "@mono/ui/switch";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { toast } from "sonner";
import { Button } from "@mono/ui/button";
import { useState, useEffect } from "react";
import { MediaUpload } from "../customizedAICharacter/AdvanceUpload";
// import uploadFileToOSS from '@/utils/ossUpload'; 
import uploadFileToOSS from "@/api/uploadFileToOSS ";

export function AppInfoIndex() {
  const queryClient = useQueryClient();
  const { data: appInfoList } = useQuery({
    queryKey: ['fetchAppInfo'],
    queryFn: () => fetchAppInfo({}),
  });

  const [formData, setFormData] = useState<AppInfoResponse | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [editingFields, setEditingFields] = useState<Record<string, boolean>>({});
  const [originalValues, setOriginalValues] = useState<Record<string,any>>({});
  const [fileUploading, setFileUploading] = useState(false);

  useEffect(() => {
    if (appInfoList) {
      setFormData(appInfoList);
      setHasChanges(false);
      setEditingFields({});
    }
  }, [appInfoList]);

  const saveAppInfo = useMutation({
    mutationFn: (data: AppInfoResponse) => saveConfiguration(data),
    onSuccess: () => {
      toast.success('配置已保存');
      queryClient.invalidateQueries({ queryKey: ['fetchAppInfo'] });
      setHasChanges(false);
      setEditingFields({});
    },
    onError: () => {
      toast.error('保存失败，请重试');
    }
  });

  const handleSwitchChange = (key: keyof AppInfoResponse, value: boolean) => {
    if (formData) {
      setFormData({ ...formData, [key]: value });
      setHasChanges(true);
    }
  };

  const handleEditClick = (field: string) => {
    if (!formData) return;
    setEditingFields(prev => ({ ...prev, [field]: true }));
    setOriginalValues(prev => ({ ...prev, [field]: formData[field as keyof AppInfoResponse] }));
  };

  const handleCancelEdit = () => {
    if (!formData || !appInfoList) return;

    const newFormData = { ...formData };
    Object.keys(editingFields).forEach(field => {
      if (field in originalValues) {
        (newFormData as any)[field] = originalValues[field];
      }
    });

    setFormData(newFormData);
    setEditingFields({});
    setOriginalValues({});
    setHasChanges(false);
  };

  const handleTextChange = (field: string, value: string) => {
    if (formData) {
      setFormData({ ...formData, [field]: value });
      setHasChanges(true);
    }
  };

  const handleFileUpload = async (file: File | undefined, field: string) => {
    if (!file || !formData) return;

    try {
      setFileUploading(true);
      const fileUrl = await uploadFileToOSS(file);
      setFormData({ ...formData, [field]: fileUrl });
      setHasChanges(true);
      setFileUploading(false);
    } catch (error) {
      setFileUploading(false);
      toast.error('文件上传失败');
      console.error('oss上传失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData || !hasChanges) {
      toast.info('没有需要保存的修改');
      return;
    }

    try {
      await saveAppInfo.mutateAsync(formData);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  if (!appInfoList || !formData) {
    return <div className="p-4"></div>;
  }

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6 p-4 max-w-xl">
      <div className="flex items-start gap-3 p-4 rounded-lg border-b">
        <div className="flex items-start gap-3 w-full">
          <img src={RequiredIco} className="w-3.5 h-3.5 mt-1 flex-shrink-0" alt="必填图标" />
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">打开视频下载开关</h3>
              <Switch
                checked={formData.WebVideoDownloadSwitch}
                onCheckedChange={(checked) => handleSwitchChange('WebVideoDownloadSwitch', checked)}
              />
            </div>
            <p className="text-gray-500 text-sm mt-1">
              打开后，将展示下载链接
            </p>
          </div>
        </div>
      </div>

      <div className="flex flex-col p-4 rounded-lg gap-2 border-b">
        <div className="flex justify-between items-center">
          <h3 className="font-medium">小程序分享卡片链接</h3>

        </div>

        {editingFields.appletShareCardUrl ? (
          <input
            type="text"
            value={formData.appletShareCardUrl || ''}
            onChange={(e) => handleTextChange('appletShareCardUrl', e.target.value)}
            className="border rounded p-2 text-sm"
          />
        ) : (
          <div className="flex justify-between items-center mt-2">
            <div className="break-all text-sm text-blue-700 hover:underline cursor-pointer">
              {formData.appletShareCardUrl || "卡片链接地址"}
            </div>
            <button
              type="button"
              onClick={() => handleEditClick('appletShareCardUrl')}
              className="text-xs text-blue-500 hover:text-blue-700"
            >
              修改
            </button>
          </div>
        )}

        <div className="flex justify-between items-center mt-2">
          <p className="text-gray-500 text-xs">
            "{formData.appletShareTips}"
          </p>
          <button
            type="button"
            onClick={() => handleEditClick('appletShareTips')}
            className="text-xs text-blue-500 hover:text-blue-700"
          >
            修改
          </button>
        </div>

        {editingFields.appletShareTips && (
          <input
            type="text"
            value={formData.appletShareTips || ''}
            onChange={(e) => handleTextChange('appletShareTips', e.target.value)}
            className="border rounded p-2 text-sm mt-2"
          />
        )}
      </div>

      <div className="flex items-start gap-3 p-4 rounded-lg border-b">
        <div className="flex items-start gap-3 w-full">
          <MediaUpload
            onChange={(file) => handleFileUpload(file, 'guidanceCourseVideoUrl')}
            initialUrl={formData.guidanceCourseVideoUrl}
            fileType='video'
          />
          <img src={RequiredIco} className="w-3.5 h-3.5 mt-1 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">开启教程引导</h3>
              <Switch
                checked={formData.guidanceCourseSwitch}
                onCheckedChange={(checked) => handleSwitchChange('guidanceCourseSwitch', checked)}
              />
            </div>
            {/* {formData.guidanceCourseVideoUrl && (
              <p className="text-gray-700 text-xs mt-1 break-all ">
                视频链接: {formData.guidanceCourseVideoUrl}
              </p>
            )} */}
          </div>
        </div>
      </div>

      <div className="flex items-start gap-3 w-full p-4 border-b">
        <MediaUpload
          onChange={(file) => handleFileUpload(file, 'serviceQrCodeUrl')}
          initialUrl={formData.serviceQrCodeUrl || ''}
          fileType='image'
        />
        <div className="flex flex-col p-4 rounded-lg gap-2 flex-1">
          <div className="flex justify-between items-center">
            <h3 className="font-medium">客服二维码</h3>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-gray-500 text-sm">
              {formData.serviceTips}
            </p>
            <button
              type="button"
              onClick={() => handleEditClick('serviceTips')}
              className="text-xs text-blue-500 hover:text-blue-700"
            >
              修改
            </button>
          </div>

          {editingFields.serviceTips && (
            <input
              type="text"
              value={formData.serviceTips || ''}
              onChange={(e) => handleTextChange('serviceTips', e.target.value)}
              className="border rounded p-2 text-sm mt-2"
            />
          )}
        </div>
      </div>

      <div className="text-center item-center flex w-full mx-auto justify-center gap-4">
        {hasChanges && (
          <Button
            type="button"
            variant="outline"
            onClick={handleCancelEdit}
            className="w-28 rounded-sm"
            disabled={saveAppInfo.isPending || fileUploading}
          >
            取消
          </Button>
        )}
        <Button
          type="submit"
          className="w-28 rounded-sm"
          disabled={saveAppInfo.isPending || !hasChanges || fileUploading}
        >
          {saveAppInfo.isPending || fileUploading ? '保存中...' : '保存'}
        </Button>
      </div>
    </form>
  );
}