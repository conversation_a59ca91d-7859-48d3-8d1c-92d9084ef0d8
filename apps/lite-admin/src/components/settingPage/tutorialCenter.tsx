import RequiredIco from '@/assets/svg/required_ico.svg';
import { Button } from '@mono/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from '@mono/ui/dialog';
import { Switch } from '@mono/ui/switch';
import { MediaUpload } from '../customizedAICharacter/AdvanceUpload';
import { Input } from '@mono/ui/input';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { addCourseVideoList, deleteCourseVideo, fetchCourseVideoList, FetchTorialCenterResponse, updateCourseVideoList } from '@/api/settingPage/tutorialCenter';
import { DataTable } from '../dataTable';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { useState } from 'react';
import { toast } from 'sonner';
import uploadFileToOSS from '@/api/uploadFileToOSS ';

export function TutorialCenter() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [formData, setFormData] = useState<Partial<FetchTorialCenterResponse>>({});
  const [fileUploading, setFileUploading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const columns: ColumnDef<FetchTorialCenterResponse, unknown>[] = [
    {
      header: '视频',
      accessorKey: 'videoUrl',
      cell: ({ row }) => (
        <span>
          <img src={row.original.coverImageUrl || row.original.videoUrl || ''} alt="视频封面" className="h-16 w-16 object-cover" />
        </span>
      ),
    },
    {
      header: '名称',
      accessorKey: 'title',
      cell: ({ row }) => <span>{row.original.title || ''}</span>,
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className='flex gap-2 text-blue-500 cursor-pointer'>
          <span onClick={() => handleEdit(row.original)}>编辑</span>
          <span className='text-red-500' onClick={() => deleteMutation.mutate(row.original._id as string)}>删除</span>
        </div>
      ),
    },
  ];

  const queryClient = useQueryClient();
  const { data: videoList } = useQuery({
    queryKey: ['fetchCourseVideoList'],
    queryFn: () => fetchCourseVideoList({}),
  });

  const repairMutation = useMutation({
    mutationFn: addCourseVideoList,
    onSuccess: () => {
      toast.success('新增成功');
      queryClient.invalidateQueries({ queryKey: ['fetchCourseVideoList'] });
      setDialogOpen(false);
      setFormData({});
    },
    onError: (error) => {
      toast.error(`新增失败: ${error.message}`);
    }
  });

  const updateMutation = useMutation({
    mutationFn: updateCourseVideoList,
    onSuccess: () => {
      toast.success('保存成功');
      queryClient.invalidateQueries({ queryKey: ['fetchCourseVideoList'] });
      setDialogOpen(false);
      setFormData({});
    },
    onError: (error) => {
      toast.error(`保存失败: ${error.message}`);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: deleteCourseVideo,
    onSuccess: () => {
      toast.success('删除成功');
      queryClient.invalidateQueries({ queryKey: ['fetchCourseVideoList'] });
      setDialogOpen(false);
      setFormData({});
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  const handleTextChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleVideoUpload = async (file: File | undefined, field: string) => {
    if (!file) return;

    try {
      setFileUploading(true);
      const fileUrl = await uploadFileToOSS(file);
      setFormData(prev => ({ ...prev, [field]: fileUrl }));
    } catch (error) {
      toast.error('上传失败!');
    } finally {
      setFileUploading(false);
    }
  };

  const handleEdit = (video: FetchTorialCenterResponse) => {
    setFormData(video);
    setDialogOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.videoUrl) {
      toast.error('请上传视频文件');
      return;
    }

    try {
      if (formData._id) {
        await updateMutation.mutateAsync({
          ...formData,
          _id: formData._id,
          voiceSort: formData.voiceSort || '1000'
        });
      } else {
        await repairMutation.mutateAsync({
          ...formData,
          _id: formData._id,
          voiceSort: String(1000 + (videoList?.length || 0)),//暂时
        });
      }
    } catch (error) {
      console.error("操作失败!", error);
    }
  };

  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      setFormData({});
    }
    setDialogOpen(open);
  };

  return (
    <div className="flex flex-col gap-6 p-4 max-w-[80%] ">
      <div className="flex flex-col gap-2 overflow-hidden">

        {/* 字段待补充 */}
        <div className='flex'>
          <div>
            <img src={RequiredIco} className='w-[14px] h-[14px]' alt="必填" />
          </div>
          <div>功能开关</div>
          <div className='flex-col ml-4'>
            <div>
              <Switch />
            </div>
            <small className='text-gray-500 text-xs'>用户首次打开小程序时展示的教程引导视频</small>
          </div>
        </div>

        <div className='flex'>
          <div>
            <img src={RequiredIco} className='w-[14px] h-[14px]' alt="必填" />
          </div>
          <div>教程视频</div>
          <div className='ml-4'>
            <Dialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
              <DialogTrigger asChild>
                <Button className="w-24 h-8 text-xs">+添加视频</Button>
              </DialogTrigger>

              <DialogContent className="w-[600px]">
                <DialogHeader className="text-center border-b pb-4 text-lg font-medium">
                  {formData._id ? '编辑视频' : '添加视频'}
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-6 py-2">
                  {/* 视频文件上传 */}
                  <div className="flex items-start gap-4">
                    <div className="flex w-40 items-center justify-end gap-2">
                      <img src={RequiredIco} className="w-4 h-3.5" alt="必填" />
                      <span className="text-sm">视频文件</span>
                    </div>
                    <div className="flex-1">
                      <MediaUpload
                        onChange={(file) => handleVideoUpload(file, 'videoUrl')}
                        initialUrl={formData?.videoUrl || ''}
                        fileType="video"
                      />
                      <div className="mt-2 text-xs text-gray-500 space-y-1">
                        <p>建议上传720 * 1280的竖屏视频，大小＜100MB，</p>
                        <p>以获得更好的播放体验</p>
                      </div>
                    </div>
                  </div>

                  {/* 视频封面上传 */}
                  <div className="flex items-start gap-4">
                    <div className="flex w-40 items-center justify-end gap-2">
                      <img src={RequiredIco} className="w-4 h-3.5" alt="?" />
                      <span className="text-sm">视频封面</span>
                    </div>
                    <div className="flex-1">
                      <MediaUpload
                        onChange={(file) => handleVideoUpload(file, 'coverImageUrl')}
                        initialUrl={formData?.coverImageUrl || ''}
                        fileType="image"
                      />
                      <div className="mt-2 text-xs text-gray-500 space-y-1">
                        <p>显示在教程中心的图片</p>
                        <p>建议上传720 * 1280，大小＜2MB，格式为jpg或png的图片</p>
                        <p>不上传，则提交时，默认使用视频的第一帧作为封面</p>
                      </div>
                    </div>
                  </div>

                  {/* 视频标题 */}
                  <div className="flex items-center gap-4">
                    <div className="flex w-40 items-center justify-end gap-2">
                      <img src={RequiredIco} className="w-4 h-3.5" alt="必填" />
                      <span className="text-sm">视频标题</span>
                    </div>
                    <Input
                      className="flex-1"
                      placeholder="请输入视频标题"
                      value={formData?.title || ''}
                      onChange={(e) => handleTextChange('title', e.target.value)}
                      required
                    />
                  </div>

                  <DialogFooter className="border-t pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setDialogOpen(false)}
                    >
                      取消
                    </Button>
                    <Button
                      type="submit"
                      disabled={repairMutation.isPending || fileUploading}
                    >
                      {repairMutation.isPending || fileUploading ? '处理中...' : '确定'}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <DataTable
          columns={columns}
          data={Array.isArray(videoList) ? videoList : []}
          pagination={pagination}
          setPagination={setPagination}
        />
      </div>
    </div>
  );
}