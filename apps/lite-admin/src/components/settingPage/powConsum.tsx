import { fetchHashrateConfig, saveConfiguration } from '@/api/settingPage/powConsum';
import { HashrateFormValues } from '@/types/powConsum';
import { Form, FormControl, FormField, FormItem } from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button'; // 确保引入了Button组件
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

interface PowerConsumptionItem {
  id: string;
  name: string;
  unit: string;
  defaultValue?: number;
  isHighSpeedSupported: boolean;
  highSpeedRate?: number;
}

export function PowConsumIndex() {
  const [initialValues, setInitialValues] = useState<HashrateFormValues | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  
  const form = useForm<HashrateFormValues>({
    defaultValues: {
      genSzrHashrate: '',
      genVoiceHashrate: '',
      genVideoHashrate: '',
      genIntelligentVideoHashrate: '',
      genTopspeedVideoHashrate: '',
      copywritingExtractionHashrate: '',
      audioSamplePlaybackHashrate: '',
      dpskNarrationHashrate: '',
      narrationHashrate: '',
      copywritingRevisionHashrate: '',
      genTitleHashrate: '',
      genXHSHashrate: '',
      translateHashrate: '',
      wxMomentsHashrate: '',
      searchType: 1,
      startDate: '',
      endDate: '',
    },
  });

  const { data: hashrateConfig } = useQuery({
    queryKey: ['fetchHashrateConfig'],
    queryFn: () => fetchHashrateConfig({
      productName: '',
      hashrate: 0,
      price: 0
    }),
  });

  useEffect(() => {
    if (hashrateConfig) {
      setInitialValues(hashrateConfig);
      form.reset(hashrateConfig);
    }
  }, [hashrateConfig, form]);

  useEffect(() => {
    const subscription = form.watch((value,) => {
      if (initialValues && JSON.stringify(value) !== JSON.stringify(initialValues)) {
        setIsDirty(true);
      } else {
        setIsDirty(false);
      }
    });
    return () => subscription.unsubscribe();
  }, [form, initialValues]);

  const handleCancel = () => {
    if (initialValues) {
      form.reset(initialValues);
    }
    setIsDirty(false);
  };

  const handleSave = async () => {
    try {
      const values = form.getValues();
      await saveConfiguration(values);
      setInitialValues(values);
      setIsDirty(false);
      toast.success('保存成功!')
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  const powerConsumptionItems: PowerConsumptionItem[] = [
    { id: 'genSzrHashrate', name: '定制数字人', unit: '算力/个', isHighSpeedSupported: false },
    { id: 'genVoiceHashrate', name: '克隆声音', unit: '算力/个', isHighSpeedSupported: false },
    { id: 'genVideoHashrate', name: '数字人生成视频', unit: '算力/分钟', isHighSpeedSupported: true, highSpeedRate: 50 },
    { id: 'genIntelligentVideoHashrate', name: '智能剪辑视频生成', unit: '算力/分钟', isHighSpeedSupported: true, highSpeedRate: 70 },
    { id: 'genTopspeedVideoHashrate', name: '发布视频算力', unit: '算力/Mb/次', isHighSpeedSupported: false },
    { id: 'copywritingExtractionHashrate', name: '视频文案提取', unit: '算力/次/1000字 (超过最多字折叠2次)', isHighSpeedSupported: false },
    { id: 'audioSamplePlaybackHashrate', name: '音频试听', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'dpskNarrationHashrate', name: 'deepseek口播文案', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'narrationHashrate', name: '口播文案', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'copywritingRevisionHashrate', name: '文案改写', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'genTitleHashrate', name: '标题生成', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'genXHSHashrate', name: '小红书笔记', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'translateHashrate', name: '翻译', unit: '算力/次', isHighSpeedSupported: false },
    { id: 'wxMomentsHashrate', name: '朋友圈营销', unit: '算力/次', isHighSpeedSupported: false },
  ];

  return (
    <div className="flex flex-col p-4 w-[90%] max-w-4xl ml-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">算力消耗规则说明</h2>
        <div className="flex gap-2">
          {isDirty && (
            <Button
              variant="outline"
              onClick={handleCancel}
              className='w-24'
            >
              取消
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!isDirty}
            className='w-24'
          >

            保存
          </Button>
        </div>
      </div>

      <div className="w-full flex bg-gray-100 dark:bg-black p-2 pl-0 border ">
        <div className="w-[28%] font-semibold pl-2">计算功能</div>
        <div className="w-[44%] font-semibold pl-2">计费方式</div>
        <div className="w-[28%] font-semibold pl-2">极速算力</div>
      </div>

      <Form {...form}>
        <div className="border-l border-r border-b">
          {powerConsumptionItems.map((item) => (
            <div key={item.id} className="w-full flex border-b last:border-b-0">
              <div className="w-[28%] text-sm p-2 border-r flex items-center bg-gray-100 dark:bg-black">
                {item.name}
              </div>
              <div className="w-[44%] p-2 border-r">
                <FormField
                  control={form.control}
                  name={item.id as any}
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-2 m-0">
                      <FormControl>
                        <div className="flex items-center gap-2 w-full">
                          <Input
                            className="w-30 h-8"
                            value={field.value || ''}
                            onChange={(e) => {
                              const value = e.target.value.replace(/[^0-9]/g, '');
                              field.onChange(value);
                            }}
                          />
                          <span className="text-sm text-gray-500">{item.unit}</span>
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <div className="w-[28%] text-sm p-2 flex items-center">
                {item.isHighSpeedSupported
                  ? `${item.highSpeedRate} ${item.unit}`
                  : '不支持极速算力消耗'}
              </div>
            </div>
          ))}
        </div>
      </Form>
    </div>
  );
}