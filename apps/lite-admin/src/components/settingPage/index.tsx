
import { useEffect, useRef, useState } from "react";
import { PowConsumIndex } from "./powConsum";
import { PowPackageIndex } from "./powPackage";
import { ScrollArea } from "@mono/ui/scroll-area";
import { AppInfoIndex } from "./appInfo";
import { TutorialCenter } from "./tutorialCenter";


const tabItems = [
  { key: 'info', label: '应用信息', component: <AppInfoIndex /> },
  { key: 'center', label: '教程中心', component: <TutorialCenter /> },
  { key: 'consumption', label: '算力消耗', component: <PowConsumIndex /> },
  { key: 'package', label: '算力包', component: <PowPackageIndex /> },
];

export function SettingIndex() {
  const [activeTab, setActiveTab] = useState<string>('info');
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 });
  const tabRefs = useRef<Record<string, HTMLDivElement>>({});

  useEffect(() => {
    const el = tabRefs.current[activeTab];
    if (el) {
      const { offsetLeft, offsetWidth } = el;
      setIndicatorStyle({ left: offsetLeft - 32, width: offsetWidth });
    }
  }, [activeTab]);


  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="p-6">
      <div className="font-bold text-[18px] p-5 pl-0">设置中心</div>
        <div className="relative flex space-x-8 border-b mb-4 pb-2">
          {tabItems.map((item) => (
            <div
              key={item.key}
              ref={(el) => {
                if (el) tabRefs.current[item.key] = el;
              }}
              onClick={() => setActiveTab(item.key)}
              className={`cursor-pointer transition-colors duration-300 ${activeTab === item.key ? 'text-green-600' : ''
                }`}
            >
              {item.label}
            </div>
          ))}
          <div
            className="absolute bottom-0 h-0.5 bg-green-600 transition-all duration-300 ease-in-out"
            style={{
              left: `${indicatorStyle.left}px`,
              width: `${indicatorStyle.width}px`,
            }}
          />
        </div>


        <div>
          {tabItems.find((item) => item.key === activeTab)?.component}
        </div>
        
      </div>
    </ScrollArea>

  )
}
