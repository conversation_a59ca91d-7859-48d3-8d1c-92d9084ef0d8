import { <PERSON><PERSON><PERSON><PERSON> } from "@mono/ui/scroll-area";
import { DataTable } from "../dataTable";
import { useState } from "react";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
import { OperationLogItem } from "@/types/actionLog";
import { fetchOperationLogs } from "@/api/actionLog";
import { useQuery } from "@tanstack/react-query";
import { formatDate } from "@mono/utils/day";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@mono/ui/select';
import { Input } from "@mono/ui/input";
import { Button } from "@mono/ui/button";
import { Label } from "@mono/ui/label";
import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from "@/components/DatePickerWithRange";

interface SearchParams {
  searchType?: number;
  searchText: string;
  action: string;
  module: string;
  startDate?: number;
  endDate?: number;
}
interface FormState {
  searchType?: number;
  searchText: string;
  action: string;
  module: string;
  dateRange?: DateRange; 
}

export function ActionLogIndex() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [formState, setFormState] = useState<FormState>({
    searchType: 2,
    searchText: '',
    action: '',
    module: '',
    dateRange: undefined
  });

  const [searchParams, setSearchParams] = useState<SearchParams>({
    searchType: undefined,
    searchText: '',
    action: '',
    module: '',
    startDate: undefined,
    endDate: undefined
  });

  const { data: operationLogs } = useQuery({
    queryKey: ['fetchOperationLogs', pagination, searchParams],
    queryFn: () => fetchOperationLogs({
      page: pagination.pageIndex + 1,
      size: pagination.pageSize,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      action: searchParams.action || undefined,
      module: searchParams.module || undefined,
      startDate: searchParams.startDate ? Math.floor(searchParams.startDate / 1000) : undefined,
      endDate: searchParams.endDate ? Math.floor(searchParams.endDate / 1000) : undefined,
    }),
  });

  const handleSearch = () => {
    setSearchParams({
      searchType: formState.searchType,
      searchText: formState.searchText,
      action: formState.action,
      module: formState.module,
      startDate: formState.dateRange?.from?.getTime(),
      endDate: formState.dateRange?.to?.getTime()
    });
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const handleReset = () => {
    setFormState({
      searchType: undefined,
      searchText: '',
      action: '',
      module: '',
      dateRange: undefined
    });
    setSearchParams({
      searchType: undefined,
      searchText: '',
      action: '',
      module: '',
      startDate: undefined,
      endDate: undefined
    });
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const handleDateChange = (range: DateRange | undefined) => {
    setFormState(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  const columns: ColumnDef<OperationLogItem, unknown>[] = [
    {
      header: '操作时间',
      cell: ({ row }) => (
        <span>
          {formatDate(new Date(row.original.createdAt).getTime())}
        </span>
      ),
    },
    {
      header: '操作人名称',
      cell: ({ row }) => <span>{row.original.admin.username}</span>,
    },
    {
      header: '操作模块',
      accessorKey: 'module',
    },
    {
      header: '操作类型',
      accessorKey: 'action',
    },
    {
      header: '操作内容',
      accessorKey: 'content',
      cell: ({ row }) => (
        <div className=" truncate" title={row.original.content}>
          {/* max-w-[300px] */}
          {row.original.content}
        </div>
      ),
    },
  ];

  const actionOptions = {
    '设置': ['修改设置', '更新配置', '重置设置'],
    '应用': ['创建应用', '更新应用', '删除应用'],
    '交易': ['创建订单', '取消订单', '退款'],
    '用户': ['创建用户', '更新用户', '禁用用户'],
    '审核': ['通过审核', '拒绝审核', '重新审核'],
    '系统': ['系统更新', '维护开始', '维护结束']
  };

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div>
        <h2 className="text-lg font-semibold p-4">操作日志</h2>
        <div className="flex flex-col p-4 space-y-4">
          <div className="flex flex-wrap items-center gap-4">
            <Label className="whitespace-nowrap">搜索条件</Label>
            <Select
              value={String(formState.searchType)}
              onValueChange={(value) => setFormState({
                ...formState,
                searchType: Number(value),
                searchText: ''
              })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="请选择" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='1'>手机号</SelectItem>
                <SelectItem value='2'>操作人</SelectItem>
              </SelectContent>
            </Select>

            <Input
              className="w-48"
              placeholder={formState.searchType === 1 ? '请输入手机号' : '请输入操作人'}
              value={formState.searchText}
              onChange={(e) => setFormState({
                ...formState,
                searchText: e.target.value
              })}
            />

            <Label className="whitespace-nowrap">操作模块</Label>
            <Select
              value={formState.module}
              onValueChange={(value) => setFormState({
                ...formState,
                module: value,
                action: '',
              })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="全部模块" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部模块</SelectItem>
                <SelectItem value='设置'>设置</SelectItem>
                <SelectItem value='应用'>应用</SelectItem>
                <SelectItem value='交易'>交易</SelectItem>
                <SelectItem value='用户'>用户</SelectItem>
                <SelectItem value='审核'>审核</SelectItem>
                <SelectItem value='系统'>系统</SelectItem>
              </SelectContent>
            </Select>

            {formState.module && (
              <>
                <Label className="whitespace-nowrap">操作类型</Label>
                <Select
                  value={formState.action}
                  onValueChange={(value) => setFormState({
                    ...formState,
                    action: value
                  })}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="全部类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    {actionOptions[formState.module as keyof typeof actionOptions]?.map(action => (
                      <SelectItem key={action} value={action}>{action}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </>
            )}
          </div>

          <div className="flex flex-wrap items-center gap-4">
            <Label className="whitespace-nowrap">操作时间</Label>
            <DatePickerWithRange
              className="w-64"
              value={formState.dateRange}
              onChange={handleDateChange}
            />

            <div className="flex gap-2 ml-auto">
              <Button
                variant="outline"
                onClick={handleReset}
              >
                重置
              </Button>
              <Button onClick={handleSearch}>搜索</Button>
            </div>
          </div>
        </div>
      </div>

      <div className="w-[95%] mx-auto">
        <DataTable
          columns={columns}
          data={operationLogs?.data || []}
          pagination={pagination}
          setPagination={setPagination}
          rowCount={operationLogs?.totalSize || 0}
        />
      </div>
    </ScrollArea>
  );
}