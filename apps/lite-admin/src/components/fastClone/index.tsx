import { DataTable } from '@/components/dataTable';
import { getTableColumns } from './columns';
import { PaginationState } from '@tanstack/react-table';
import { useState, useEffect, useMemo } from 'react';
import { TableSearch } from './TableSearch';
import { fastClonEConfigurationItem, fastCloneList } from '@/api/fastClone';
import { fastCloneParams, userFastCloneInfo } from '@/types/fastClone';
import { ScrollArea } from '@mono/ui/scroll-area';
import { useQuery } from '@tanstack/react-query';
import { Check } from "lucide-react";
import { removeFastCloneList } from '@/api/fastClone';
import { Alert } from '@mono/ui/alert';
import { LoadingContainer } from '../loading';


export function FastClonePage() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [searchParams, setSearchParams] = useState<fastCloneParams>({
    canTopspeedRange: undefined,//1|2
    searchType: 1,
    searchText: '',
    startDate: '',
    endDate: ''
  });


  const [tableData, setTableData] = useState<userFastCloneInfo[]>([]);
  const [totalSize, setTotalSize] = useState<number>(0);

  const apiParams = useMemo(() => {
    const params: fastCloneParams = {
      page: pagination.pageIndex,
      size: pagination.pageSize,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      canTopspeedRange: searchParams.canTopspeedRange
    };
    if (searchParams.startDate) {
      params.startDate = new Date(searchParams.startDate).toISOString();
    }
    if (searchParams.endDate) {
      params.endDate = new Date(searchParams.endDate).toISOString();
    }
    return params;
  }, [pagination, searchParams]);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const res = await fastClonEConfigurationItem();
        console.log(res);

        setSearchParams(prev => ({
          ...prev,
          canTopspeedRange: res.canTopspeedRange
        }))
      } catch (error) {
        console.log(error);
      }
    }
    fetchConfig()
  }, [])

  const { data, isLoading } = useQuery({
    queryKey: ['fastCloneList', apiParams],
    queryFn: async () => {
      const res = await fastCloneList(apiParams);
      return res.data || [];
    },
    staleTime: 5000,
  });
  console.log(apiParams);


  useEffect(() => {
    if (data) {
      setTableData(data);
      setTotalSize(data.length || 0);
    }
  }, [data]);


  const handleSearch = (params: fastCloneParams) => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
    setSearchParams(params);
  };

  const [masterSwitch, setMasterSwitch] = useState(0 | 1);
  const [showAlert, setShowAlert] = useState(false);

  const handleDelete = async (_id?: string) => {
    if (!_id) return;
    try {
      await removeFastCloneList({ _id });
      setPagination((prev) => ({ ...prev }));
      setShowAlert(true);
      setTimeout(() => {
        setShowAlert(false);
      }, 3000)

    } catch (error) {
      console.error('删除失败', error);
    }
  };

  const columns = useMemo(
    () => getTableColumns(searchParams.canTopspeedRange ?? 1, handleDelete),
    [searchParams.canTopspeedRange]
  ); 1



  return (
    <ScrollArea className="h-full">
      <div className="flex flex-col gap-2 overflow-hidden">
        {showAlert && (
          <div className="fixed inset-x-0 top-0 flex justify-center items-start mt-[20px] z-50">
            <Alert
              variant="default"
              className="
              flex flex-col items-center 
              justify-center  p-4 
              rounded-md shadow-xl w-[130px] 
              pl-1 pr-0 h-10 transition 
              duration-300"
            >
              <Check color="white" className="w-6 h-6 transform scale-[0.7]  bg-green-500 rounded-xl mt-[-8px]" />
              <span className="text-sm text-center">删除成功！</span>
            </Alert>
          </div>
        )}

        <TableSearch
          values={searchParams}
          onSearch={handleSearch}
          // masterSwitch={masterSwitch} 
          onChangeMasterSwitch={setMasterSwitch}
        />

        {masterSwitch === 1 && (
          isLoading ? (
            <LoadingContainer />
          ) : (
            <DataTable
              columns={columns}
              data={tableData}
              rowCount={totalSize}
              pagination={pagination}
              setPagination={setPagination}
            />
          )
        )}
      </div>
    </ScrollArea>
  );
}
