import { CellContext, ColumnDef } from '@tanstack/react-table';
import { userFastCloneInfo } from '@/types/fastClone';
import { formatDate } from '@mono/utils/day';
// import { removeFastCloneList } from '@/api/fastClone';

export const getTableColumns = (
  canTopspeedRange: 1 | 2,
  onDelete: (_id?: string) => void
): Array<ColumnDef<userFastCloneInfo>> => [
    {
      header: '用户编号',
      accessorKey: '_id',
      cell: ({ row }) => <span>{row.original._id || ''}</span>,
    },
    {
      header: '手机号',
      accessorKey: 'phone',
      cell: ({ row }) => <span>{row.original.phone || ''}</span>,
    },
    {
      header: '用户名',
      accessorKey: 'nickName',
      cell: ({ row }) => <span>{row.original.nickName || ''}</span>,
    },
    {
      header: '添加时间',
      accessorKey: 'createdAt',
      cell: ({ row }) =>
        canTopspeedRange === 1 ? (
          <span>--</span>
        ) : (
          <span>{formatDate(new Date(row.original.createdAt!).getTime()) || '--'}</span>
        ),
    },
    {
      header: '备注',
      accessorKey: 'remarks',
      cell: ({ row }) => <span>{row.original.remarks || '--'}</span>,
    },
    ...(canTopspeedRange === 2
      ? [
        {
          header: '操作',
          accessorKey: 'delete',
          cell: ({ row }:CellContext<userFastCloneInfo,unknown>) => (
            <span
              className="text-red-500 cursor-pointer"
              onClick={() => onDelete(row.original._id)}
            >
              移除
            </span>
          ),
        },
      ]
      : []),
  ];
