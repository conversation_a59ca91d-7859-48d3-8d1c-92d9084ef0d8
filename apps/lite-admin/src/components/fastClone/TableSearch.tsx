import { useForm } from 'react-hook-form';
import { useEffect, useState } from 'react';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { isNil, omitBy } from 'lodash';
import { fastCloneParams } from '@/types/fastClone';
import { CustomSelect } from '@mono/ui/common/CustomSelect';
import { Switch } from '@mono/ui/switch';
import { DateRange } from 'react-day-picker';
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { format, } from 'date-fns';
import { fastClonEConfigurationItem, fastClonePreserve, addFastCloneList } from '@/api/fastClone';

import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@mono/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Label } from '@mono/ui/label';
import { LoadingButton } from '@/components/loading-button';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Check } from "lucide-react";
import { Alert } from '@mono/ui/alert';





type SearchForm = fastCloneParams;

export function TableSearch({
  values,
  onSearch,
  onChangeMasterSwitch,
}: {
  values?: fastCloneParams;
  onSearch: (values: fastCloneParams) => void;
  onChangeMasterSwitch?: (value: 0 | 1) => void;
}) {

  const currentDate = new Date(new Date().setHours(0, 0, 0, 0))
  const [dateRange, setDateRange] = useState<DateRange>({ from: undefined, to: undefined });



  const form = useForm<SearchForm>({
    defaultValues: {
      masterSwitch: 1,
      topspeedHashrate: undefined,
      canTopspeedRange: undefined,
      searchType: 1,
      startDate: '',
      endDate: '',
      ...values,
    },
  });

  const onSubmit = (formData: SearchForm) => {
    const processedData = {
      ...formData,
      searchText: formData.searchText,
      startDate: formData.startDate ? new Date(formData.startDate).getTime() : '',
      endDate: formData.endDate ? new Date(formData.endDate).getTime() : ''
    };

    const params = omitBy(processedData, isNil);
    onSearch(params);
  };
  const handleDateChange = (range: { from?: Date; to?: Date }) => {
    let to = range.to;
    setDateRange({
      from: range.from,
      to: range.to,
    });

    if (to && to > currentDate) {
      to = currentDate;
      form.setValue('endDate', currentDate.toISOString());
    }

    form.setValue('startDate', range.from ? format(range.from, 'yyyy-MM-dd') : '');
    form.setValue('endDate', range.to ? format(range.to, 'yyyy-MM-dd') : '');
    form.setValue('startDate', range.from ? format(range.from, 'yyyy-MM-dd') : '');
    form.setValue('endDate', to ? format(to, 'yyyy-MM-dd') : '');
  };

  const handleRangeChange = (value: string) => {
    const numValue = parseInt(value, 10);
    if (numValue === 1 || numValue === 2) {
      form.setValue('canTopspeedRange', numValue);
    } else {
      form.setValue('canTopspeedRange', undefined);
    }
    form.handleSubmit(onSubmit)();
  };


  const masterSwitchValue = form.watch('masterSwitch');
  const canTopspeedRange = form.watch('canTopspeedRange');

  useEffect(() => {
    if (onChangeMasterSwitch !== undefined && masterSwitchValue !== undefined) {
      onChangeMasterSwitch(masterSwitchValue);
    }
  }, [masterSwitchValue, onChangeMasterSwitch]);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        const res = await fastClonEConfigurationItem();
        console.log('获取配置成功:', res);
        if (res) {
          form.setValue('masterSwitch', res.topspeedSwitch ? 1 : 0);
          form.setValue('topspeedHashrate', res.topspeedHashrate);
          form.setValue('canTopspeedRange', res.canTopspeedRange);
        }
      } catch (error) {
        console.error('获取配置失败:', error);
      }
    };
    fetchConfig();
  }, [form]);


  const postFastClone = async () => {
    try {
      const formValues = form.getValues();
      const {
        masterSwitch,
        topspeedHashrate,
        canTopspeedRange
      } = formValues;

      // 单保
      const payload = {
        masterSwitchValue: !!masterSwitch,
        topspeedHashrate: topspeedHashrate ? topspeedHashrate : 0,
        canTopspeedRange: canTopspeedRange ? canTopspeedRange : undefined,
      };

      const res = await fastClonePreserve(payload);
      form.handleSubmit(onSubmit)();
      setMessage('保存成功！')
      setShowAlert(true);
      setAddMessageAlert(false)
      setTimeout(() => {
        setShowAlert(false);
      }, 3000)
      console.log("数据传递成功", res);


    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  const loginStatus = [

    {
      value: 1,
      label: '手机号',
    },
    {
      value: 2,
      label: '用户编号',
    },
  ];


  const resetDate = () => {
    form.reset({
      ...form.getValues(),
      searchText: '',
      startDate: '',
      endDate: '',
    });

    setDateRange({ from: undefined, to: undefined });
    handleDateChange({ from: undefined, to: undefined });
    form.handleSubmit(onSubmit)();
  };




  const [addMessageAlert, setAddMessageAlert] = useState(false);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [addMethod, setAddMethod] = useState<1 | 2>(1); 
  const [userInput, setUserInput] = useState('');
  const [isValidPhone, setIsValidPhone] = useState<boolean>(true);
  const [showAlert, setShowAlert] = useState(false);
  const [message, setMessage] = useState('');


  const handleAddUserSubmit = async () => {
    try {
      const submitData = {
        [addMethod === 1 ? 'phone' : 'code']: userInput,
      };

      const response = await addFastCloneList(submitData);
      console.log('添加用户成功！', response);
      setMessage('添加成功！')
      setShowAlert(true);
      setTimeout(() => {
        setShowAlert(false);
      }, 3000)

      setAddUserDialogOpen(false);
      setUserInput('');

      form.handleSubmit(onSubmit)();
    } catch (error) {
      console.error('添加用户失败:', error);
    }
  };


  return (
    <>

        <Form {...form}>
          {/* 功能总开关盒子 */}
          <div className="border rounded-lg p-4">
            {showAlert && (
              <div className="fixed inset-x-0 top-0 flex justify-center items-start mt-[20px] z-50 ">
                <Alert
                  variant="default"
                  className="
              flex flex-col items-center 
              justify-center  p-4 
              rounded-xs shadow-xl w-[130px] 
              pl-1 pr-0 h-10 transition 
              duration-300"
                >
                  <Check color="white" className="w-6 h-6 transform scale-[0.7]  bg-green-500 rounded-xl mt-[-8px]" />
                  <span className="text-sm text-center">
                    {message}
                  </span>
                </Alert>
              </div>
            )}
            <FormField
              control={form.control}
              name="masterSwitch"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel className='text-2xl font-bold'>极速克隆</FormLabel><br />
                  <p className='whitespace-pre-line'>极速克隆，快速生成数字人视频，加速用户成交！</p>

                  <FormItem className="flex items-center gap-8 space-y-0">
                    <div className="flex items-center space-x-5">
                      <span className="text-sm font-semibold ">功能总开关</span>
                      <Switch
                        checked={field.value === 1}
                        onCheckedChange={(checked) => field.onChange(checked ? 1 : 0)}
                      />
                    </div>
                  </FormItem>

                  <small className="text-xs mt-2 block pl-24">
                    {masterSwitchValue === 1 ? (
                      <>
                        关闭后，所有定制、制作入口将隐藏<br />
                        用户所有的极速数字人、声音，将隐藏且不可使用。请谨慎关闭
                      </>
                    ) : (
                      <>
                        开启后，所有用户将看到此功能<br />
                        将通过设置下方的“可用范围”，调整使用权限
                      </>
                    )}
                  </small>

                  <Button type="submit" onClick={() => postFastClone()}>保存功能开关设置</Button>
                </FormItem>
              )}
            />
          </div>


          {masterSwitchValue === 1 && (<>
            {/* 可用算力设置盒子 */}
            <div className="border rounded-lg p-4">
              <FormField
                control={form.control}
                name="topspeedHashrate"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className='font-bold text-2xl'>可用算力设置</FormLabel><br />
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                      <Button type="submit" onClick={() => postFastClone()}>保存急速算力设置</Button>
                      <small className="text-xs">
                        该设置修改后，仅针对修改后添加的新用户生效
                      </small>
                    </div>

                    <FormItem className="flex items-center gap-8 space-y-0">
                      <FormLabel className="whitespace-nowrap font-bold">单个用户可获得极速算力值</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <Input
                            className="w-40"
                            placeholder="0"
                            value={field.value || ''}
                            onChange={(e) => field.onChange(Number(e.target.value))}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                    <FormItem className="space-y-1">
                      <small className="text-xs mt-6 block">温馨提示：已开通权限的用户可获得您设置的"极速算力"来体验极速数字人生成视频（含数字人视频和智能剪辑视频），每个用户单次有效（重复添加同一用户不能多次获得算力）,
                        用户实际消耗的算力将在您的总算力池中扣除（扣除标准为：数字人视频为50算力/每分钟；智能剪辑视频为70算力分钟）</small>
                    </FormItem>
                  </FormItem>
                )}
              />
            </div>

            {/* 可见范围设置盒子 */}
            <div className="border rounded-lg p-4">
              <FormField
                control={form.control}
                name="canTopspeedRange"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel className='font-bold text-2xl'>可见范围</FormLabel>
                    <FormControl>
                      <div className="flex space-x-6">
                        {['2', '1'].map((value) => (
                          <label key={value} className="flex items-center space-x-2 cursor-pointer">
                            <input
                              type="radio"
                              value={value}
                              checked={field.value?.toString() === value}
                              onChange={() => handleRangeChange(value)}
                              className='absolute opacity-0 h-0 w-0'
                            />
                            <span
                              className={`w-4 h-4 border rounded-full flex-shrink-0 relative ${String(field.value).trim() === String(value).trim() ? 'border-black bg-white' : 'border-gray-400'
                                }`}
                            >
                              {String(field.value).trim() === String(value).trim() && (
                                <span className="absolute inset-1 rounded-full bg-black"></span>
                              )}
                            </span>
                            <span className="text-sm font-semibold">
                              {value === '1' ? '所有人可用' : '仅指定用户可用'}
                            </span>

                          </label>
                        ))}
                      </div>

                    </FormControl>
                    {field.value === 2 && (
                      <small className="text-xs mt-10 ml-0 block">仅下方列表中的用户可用，支持批量导入<br />
                        不在范围內的用户，将提示“联系工作人员开通“</small>
                    )}


                    <Dialog open={addMessageAlert} onOpenChange={setAddMessageAlert}>
                      <DialogTrigger asChild>
                        <Button variant="default" onClick={() => setAddMessageAlert(true)}>
                          保存可用范围设置
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="!max-w-none w-[550px] p-[16px]">
                        <DialogHeader className=" p-4 pb-0 w-full items-center mt-2 pt-3">
                          <DialogTitle>保存提示</DialogTitle>
                        </DialogHeader>

                        <div className="flex flex-col gap-2 m-4  mt-0 pb-5">

                          {/* 输入框 */}
                          {canTopspeedRange == 1 ? (<div className="flex text-left justify-start h-fit-content mt-4 ">
                            请谨慎选择开放范围给全部用户，该操作将会对全量用户发放算力，全量用户的极速数字人视频消耗算力将在您的算力池余额中扣除。
                          </div>) : (
                            <div className="flex text-left justify-start h-fit-content mt-4 ">
                              是否确认保存并发布上述设置
                            </div>
                          )}


                        </div>

                        <div className='w-full flex flex-col items-center justify-center mb-4'>
                          <DialogFooter className=' pt-2 justify-center '>
                            <Button className='border border-gray-300' variant="ghost" onClick={() => setAddMessageAlert(false)}>
                              取消
                            </Button>
                            <LoadingButton
                              onClick={() => postFastClone()}
                            >
                              {canTopspeedRange == 1 ? (
                                <> 仍要开启</>
                              ) : (
                                <>保存</>
                              )}

                            </LoadingButton>
                          </DialogFooter>
                        </div>

                      </DialogContent>
                    </Dialog>
                  </FormItem>
                )}
              />

              {/* 搜索表单部分 */}
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="flex flex-wrap gap-x-1 gap-y-3 p-0.5"
              >
                <FormField
                  control={form.control}
                  name="searchType"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-3 space-y-4">
                      <FormLabel className="whitespace-nowrap mt-5 block">条件搜索</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <CustomSelect
                            options={loginStatus}
                            value={field.value}
                            onChange={field.onChange}
                            className='w-32 mr-2'
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />


                <FormField
                  control={form.control}
                  name="searchText"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-3 space-y-4">
                      <FormControl>
                        <div className="flex gap-2">
                          <Input
                            className="w-40 mt-4"
                            placeholder="请输入"
                            {...field}
                          />
                        </div>
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="startDate"
                  render={() => (
                    <FormField
                      control={form.control}
                      name="endDate"
                      render={() => (
                        <FormItem className="flex flex-col sm:flex-row items-start sm:items-center gap-3 space-y-2 sm:space-y-0">
                          <FormLabel className="whitespace-nowrap block mt-4 ml-6">添加时间</FormLabel>
                          <FormControl>
                            <DatePickerWithRange
                              className="w-[250px] !mt-4"
                              maxDate={currentDate}
                              onChange={handleDateChange}
                              value={dateRange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  )}
                />

                <FormItem className="items-center space-x-3 pl-4 mt-2">
                  <Button type="button" onClick={resetDate}>重置</Button>
                  <Button type="submit" onSubmit={form.handleSubmit(onSubmit)}>搜索</Button>
                </FormItem>
                
                <FormField
                  control={form.control}
                  name="canTopspeedRange"
                  render={({ field }) => (
                    <>
                      {field.value === 2 && (
                        <FormItem className="items-center space-x-3 pl-6 mt-4">
                          <Dialog open={addUserDialogOpen} onOpenChange={setAddUserDialogOpen} >
                            <DialogTrigger asChild>
                              <Button variant="default" onClick={() => setAddUserDialogOpen(true)}>
                                添加用户
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="!max-w-none w-[700px] p-[16px]">
                              <DialogHeader className="border-b pb-4 mb-4 w-full">
                                <DialogTitle>添加用户</DialogTitle>
                              </DialogHeader>

                              <div className="flex flex-col gap-2 m-4 ml-20 mt-4 pb-4">
                                {/* 添加方式选择 */}
                                <div className="flex items-center gap-1 space-y-0 ml-1 ">
                                  <Label className="w-24 flex-shrink-0">添加方式</Label>
                                  <RadioGroup
                                    defaultValue="1"
                                    className="flex space-x-6"
                                    onValueChange={(value) => setAddMethod(parseInt(value) as 1 | 2)}
                                  >
                                    <div className="flex items-center space-x-2 ml-[-8px]">
                                      <RadioGroupItem value="1" id="method-phone" />
                                      <Label htmlFor="method-phone">手机号</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <RadioGroupItem value="2" id="method-id" />
                                      <Label htmlFor="method-id">用户编号</Label>
                                    </div>
                                  </RadioGroup>
                                </div>

                                {/* 输入框 */}
                                <div className="flex text-left justify-start mr-4 h-fit-content mt-8 ml-[-4]">
                                  <img src={RequiredIco} className="w-[14px] h-[14px]" />
                                  <Label className="text-[14px] w-16">
                                    {addMethod === 1 ? '手机号' : '用户编号'}
                                  </Label>
                                  <div className="w-[340px] ml-4 mt-[-6px]">
                                    <Input
                                      className="w-full"
                                      placeholder={addMethod === 1 ? '请输入用户手机号' : '请输入用户编号'}
                                      value={userInput}
                                      onChange={(e) => {
                                        const value = e.target.value;
                                        setUserInput(value);

                                        // 为1验证手机号
                                        if (addMethod === 1) {
                                          const isValid = /^1[3456789]\d{9}$/.test(value);
                                          setIsValidPhone(isValid);
                                        }
                                      }}
                                    />

                                    {/* 错误提示 */}
                                    {addMethod === 1 && userInput && !isValidPhone && (
                                      <p className="text-red-500 text-sm mt-1">请输入有效的中国大陆手机号</p>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <DialogFooter className='border-t pt-4'>
                                <Button variant="ghost" className='border border-gray-300'
                                  onClick={() => setAddUserDialogOpen(false)}>
                                  取消
                                </Button>
                                <LoadingButton
                                  onClick={handleAddUserSubmit}
                                  disabled={!userInput.trim()}
                                >
                                  提交
                                </LoadingButton>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          {/* <Button type="button">批量导入</Button> */}
                        </FormItem>
                      )}
                    </>


                  )}
                />

                <>

                </>

              </form>
            </div>
          </>)}


        </Form>

    </>

  );
}