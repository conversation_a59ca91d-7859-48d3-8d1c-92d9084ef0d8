import { getRegisterScale } from '@/api/data';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { Tabs, TabsList, TabsTrigger } from '@mono/ui/tabs';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { Pie, Label, PieChart, LabelList } from 'recharts';

const chartConfig = {
  visitors: {
    label: 'Visitors',
    color: 'hsl(var(--chart-default))',
  },
  androidRegisterNum: {
    label: '安卓',
    color: 'hsl(var(--chart-1))',
  },
  iosRegisterNum: {
    label: 'ios',
    color: 'hsl(var(--chart-2))',
  },
  macRegisterNum: {
    label: 'mac',
    color: 'hsl(var(--chart-3))',
  },
  windowsRegisterNum: {
    label: 'windows',
    color: 'hsl(var(--chart-4))',
  },
} satisfies ChartConfig;

export function RegisterScale() {
  const [tab, setTab] = React.useState('all');
  const { data } = useQuery({
    queryKey: ['getRegisterScale', tab],
    queryFn: () => getRegisterScale(tab ? 'thirty' : undefined),
  });

  const chartData = React.useMemo(() => {
    return Object.entries(data || {}).map(([browser, visitors]) => ({
      browser,
      visitors,
      fill: chartConfig[browser as keyof typeof chartConfig].color,
    }));
  }, [data]);
  const totalVisitors = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.visitors, 0);
  }, [chartData]);

  return (
    <Card className="w-full">
      <CardHeader className="flex items-center flex-row justify-between py-0 h-[76px]">
        <CardTitle className="text-xl">注册占比</CardTitle>
        <Tabs value={tab} onValueChange={setTab}>
          <TabsList>
            <TabsTrigger value="all">全部</TabsTrigger>
            <TabsTrigger value="thirty">近30日</TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="w-full">
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="visitors"
              nameKey="browser"
              innerRadius={60}
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          {totalVisitors.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground"
                        >
                          总注册数
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
              <LabelList
                dataKey="browser"
                className="fill-background"
                stroke="none"
                fontSize={12}
                formatter={(value: keyof typeof chartConfig) => {
                  return chartConfig[value].label;
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
