import { useState } from "react";
import { But<PERSON> } from "@mono/ui/button";
import { Input } from "@mono/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mono/ui/select";
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { DateRange } from 'react-day-picker';


interface SelectOption {
  value: string | undefined | number;
  label: string;
}

interface BaseField {
  label: string;
  value: string;
  onChange: (value: string) => void;
}

interface SelectField extends BaseField {
  type: "select";
  options: SelectOption[];
}

interface SelectInputField extends BaseField {
  type: "select-input";
  selectOptions: SelectOption[];
  selectValue: string;
  onSelectChange: (value: string) => void;
  inputPlaceholder: (type: string) => string;
  inputValue: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

type SearchField = SelectField | SelectInputField;

interface SearchParams {
  searchType?: number;
  searchText: string;
  productName: string;
  payWay: string;
  orderStatus?: number;
  dateRange?: DateRange;
}

interface SearchPlugProps {
  packages: Array<{
    _id: string;
    productName: string;
    hashrate: number;
  }>;
  onSearch: (params: SearchParams) => void;
  onReset: () => void;
}

export const SearchPlug = ({ packages, onSearch, onReset }: SearchPlugProps) => {
  const [localParams, setLocalParams] = useState({
    searchType: undefined as number | undefined,
    searchText: "",
    productName: "",
    payWay: "",
    orderStatus: undefined as number | undefined,
    dateRange: undefined as DateRange | undefined
  });

  const handleLocalSearch = () => {
    onSearch(localParams);
  };

  const handleLocalReset = () => {
    setLocalParams({
      searchType: undefined,
      searchText: "",
      productName: "",
      payWay: "",
      orderStatus: undefined,
      dateRange: undefined
    });
    onReset();
  };

  const searchFields: SearchField[] = [
    {
      label: "条件搜索",
      type: "select-input",
      selectOptions: [
        { value: "1", label: "订单号" },
        { value: "2", label: "用户编号" },
        { value: "3", label: "用户手机号" },
        { value: "4", label: "团队名称" },
        { value: "5", label: "用户备注" },
      ],
      selectValue: localParams.searchType?.toString() || "",
      onSelectChange: (value) =>
        setLocalParams({ ...localParams, searchType: value ? parseInt(value) : undefined }),
      inputPlaceholder: (type) =>
        type === "1" ? "请输入订单号" :
          type === "2" ? "请输入用户编号" :
            type === "3" ? "请输入手机号" :
              type === "4" ? "请输入团队名称" : "请输入用户备注",
      inputValue: localParams.searchText,
      onInputChange: (e) =>
        setLocalParams({ ...localParams, searchText: e.target.value }),
      value: "",
      onChange: () => { }
    },
    {
      label: "商品名称",
      type: "select",
      options: [
        { value: undefined, label: "全部" },
        ...packages.map(pkg => ({
          value: pkg._id,
          label: pkg.productName
        }))
      ],
      value: localParams.productName,
      onChange: (value) =>
        setLocalParams({ ...localParams, productName: value })
    },
    {
      label: "支付方式",
      type: "select",
      options: [
        { value: undefined, label: "全部" },
        { value: "1", label: "后台充值" },
        { value: "2", label: "兑换码" },
        { value: "3", label: "小程序线上支付" },
        { value: "4", label: "权益包" },
        { value: "5", label: "极速算力赠送" },
      ],
      value: localParams.payWay,
      onChange: (value: string) =>
        setLocalParams({ ...localParams, payWay: value }),
    },
    {
      label: "状态",
      type: "select",
      options: [
        { value: undefined, label: "全部" },
        { value: "1", label: "未支付" },
        { value: "2", label: "已支付" },
        { value: "3", label: "支付失败" },
      ],
      value: localParams.orderStatus?.toString() || "",
      onChange: (value: string) =>
        setLocalParams({ ...localParams, orderStatus: value ? parseInt(value) : undefined }),
    },
  ];

  const renderSelect = (field: SelectField) => (
    // <Select value={field.value} onValueChange={field.onChange}>
    <Select
      value={field.value}
      onValueChange={(value) => {
        const parsedValue = value === 'undefined' ? undefined : value;
        field.onChange(parsedValue || '');
      }}
    >
      <SelectTrigger className="w-36">
        <SelectValue placeholder={field.label} />
      </SelectTrigger>
      <SelectContent>
        {field.options.map((option) => (
          <SelectItem
            key={option.value?.toString() || 'undefined'}
            value={option.value?.toString() || 'undefined'}
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );

  const renderSelectInput = (field: SelectInputField) => (
    <>
      <Select
        value={field.selectValue}
        onValueChange={field.onSelectChange}
      >
        <SelectTrigger className="w-36">
          <SelectValue placeholder={field.label} />
        </SelectTrigger>
        <SelectContent>
          {field.selectOptions.map((option) => (
            <SelectItem key={option.value?.toString()}
              value={option.value?.toString() || '全部'}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Input
        className="w-48"
        placeholder={field.inputPlaceholder(field.selectValue)}
        value={field.inputValue}
        onChange={field.onInputChange}
      />
    </>
  );


  return (
    <div className="p-4 rounded-lg shadow">
      <div className="flex flex-wrap items-center gap-4">
        {searchFields.map((field) => (
          <div key={field.label} className="flex items-center gap-2">
            <span className="whitespace-nowrap ">{field.label}</span>
            {field.type === "select-input"
              ? renderSelectInput(field)
              : renderSelect(field)}
          </div>
        ))}

        <div className="flex items-center gap-2">
          <span className="whitespace-nowrap">创建时间</span>
          <DatePickerWithRange
            className="w-64"
            onChange={(range) => setLocalParams({ ...localParams, dateRange: range })}
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={handleLocalReset} className="w-[70px]">
            重置
          </Button>
          <Button onClick={handleLocalSearch} className="w-[71px]">搜索</Button>
        </div>
      </div>
    </div>
  );
};