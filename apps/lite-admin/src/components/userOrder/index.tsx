
import { ScrollArea } from "@mono/ui/scroll-area";
import { useState, useRef, useEffect } from "react";
import { CalPackageOrderIndex } from "./calPackageOrder";

//index仅作为展示区，重点在CalPackageOrderIndex组件
const tabItems = [
  { key: 'package', label: '算力包订单', component: <CalPackageOrderIndex /> },
];

export function UserOrderIndex() {
  const [activeTab, setActiveTab] = useState<string>('package');//key存在默认展示谁
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 });
  const tabRefs = useRef<Record<string, HTMLDivElement>>({});

  useEffect(() => {
    const el = tabRefs.current[activeTab];
    if (el) {
      const { offsetLeft, offsetWidth } = el;
      setIndicatorStyle({ left: offsetLeft - 32, width: offsetWidth });
    }
  }, [activeTab]);

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="p-6">
        <div className="relative flex space-x-8 border-b mb-4 pb-2">
          {tabItems.map((item) => (
            <div
              key={item.key}
              ref={(el) => {
                if (el) tabRefs.current[item.key] = el;
              }}
              onClick={() => setActiveTab(item.key)}
              className={`cursor-pointer transition-colors font-bold text-[18px] duration-300 ${activeTab === item.key ? 'ml-4' : ''
                }`}
            >
              {item.label}
            </div>
          ))}

          <div
            className="absolute bottom-0 h-0.5 bg-black transition-all duration-300 ease-in-out "
            style={{
              left: `${indicatorStyle.left}px`,
              width: `${indicatorStyle.width}px`,
            }}
          />
        </div>

        <div>
          {tabItems.find((item) => item.key === activeTab)?.component}
        </div>

      </div>
    </ScrollArea>

  )
}
