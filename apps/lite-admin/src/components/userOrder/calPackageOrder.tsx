import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { DataTable } from '@/components/dataTable';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import { createOrder, fetchOrderList, getOrderDetail, refundOrder, updateOrder } from '@/api/useOrder';
import { Button } from '@mono/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from '@mono/ui/dialog';
import { Label } from '@mono/ui/label';
import { Input } from '@mono/ui/input';
import { fetchPowConsumList } from '@/api/settingPage/powPackage';
import { CreateOrderParams, OrderType, RefundOrderParams, UpdateOrderParams } from '@/types/userOrder';
import { toast } from 'sonner';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { MediaUpload } from '../customizedAICharacter/AdvanceUpload';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@mono/ui/dropdown-menu';
import uploadFileToOSS from '@/api/uploadFileToOSS ';
import { DateRange } from 'react-day-picker';
import { SearchPlug } from './searchPlug';
import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';


export interface SearchParams {
  searchType?: number;
  searchText: string;
  productName: string;
  payWay: string;
  orderStatus?: number;
  dateRange?: DateRange;
}

export const CalPackageOrderIndex = () => {
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [searchParams, setSearchParams] = useState<SearchParams>({
    searchType: undefined,
    searchText: "",
    productName: "",
    payWay: "",
    orderStatus: undefined,
    dateRange: undefined as DateRange | undefined
  });


  const handleSearch = (params: typeof searchParams) => {
    setSearchParams(params);
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  const handleResetSearch = () => {
    setSearchParams({
      searchType: undefined,
      searchText: "",
      productName: "",
      payWay: "",
      orderStatus: undefined,
      dateRange: undefined
    });
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  // 获取算力包订单列表
  const { data: orderList } = useQuery({
    queryKey: ['fetchOrderList', pagination, searchParams],
    queryFn: () => fetchOrderList({
      page: pagination.pageIndex + 1,
      size: pagination.pageSize,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      productName: searchParams.productName,
      payWay: searchParams.payWay,
      orderStatus: searchParams.orderStatus,
      startDate: searchParams.dateRange?.from?.getTime(),
      endDate: searchParams.dateRange?.to?.getTime()
    }),
  });



  const [orderForm, setOrderForm] = useState<CreateOrderParams>({
    productId: '',
    teamCode: '',
    attachmentUrl: '',
    description: ''
  });
  const [selectedProduct, setSelectedProduct] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  // 订单详情相关状态
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [currentOrderId, setCurrentOrderId] = useState<string | null>(null); // 明确可能为null

  const [detailForm, setDetailForm] = useState({
    attachmentUrl: '',
    description: ''
  });
  const [detailUploadedFile, setDetailUploadedFile] = useState<File | null>(null);

  const { data: orderDetail } = useQuery<OrderType>({
    queryKey: ['getOrderDetail', currentOrderId],
    queryFn: () => getOrderDetail({ orderId: currentOrderId! }), // 使用非空断言
    enabled: !!currentOrderId,
  });


  useEffect(() => {
    if (orderDetail) {
      const detail = orderDetail as { description?: string; attachmentUrl?: string };
      setDetailForm({
        attachmentUrl: detail.attachmentUrl || '',
        description: detail.description || ''
      });
    }
  }, [orderDetail]);

  const updateOrderMutation = useMutation({
    mutationFn: updateOrder,
    onSuccess: () => {
      toast.success('订单更新成功');
      queryClient.invalidateQueries({ queryKey: ['fetchOrderList'] });
      setDetailDialogOpen(false);
    },
    onError: () => {
      toast.error('订单更新失败');
    }
  });

  const refundOrderMutation = useMutation({
    mutationFn: refundOrder,
    onSuccess: () => {
      toast.success('退款成功');
      queryClient.invalidateQueries({ queryKey: ['fetchOrderList'] });
    },
    onError: () => {
      toast.error('订单更新失败');
    }
  });

  const { data: packages = [], isLoading: isPackagesLoading } = useQuery({
    queryKey: ['fetchPowConsumList'],
    queryFn: () => fetchPowConsumList({
      productName: '',
      hashrate: 0,
      price: 0
    }),
    select: (data) => Array.isArray(data) ? data : []
  });

  const createOrderMutation = useMutation({
    mutationFn: createOrder,
    onSuccess: () => {
      toast.success('订单创建成功');
      queryClient.invalidateQueries({ queryKey: ['fetchOrderList'] });
      setOpenDialog(false);
      resetForm();
    },
    onError: () => {
      toast.error('订单创建失败');
    }
  });

  const handleImageUpload = (file?: File) => {
    if (file) {
      setUploadedFile(file);
    } else {
      setUploadedFile(null);
      setOrderForm(prev => ({ ...prev, attachmentUrl: '' }));
    }
  };

  const handleDetailImageUpload = (file?: File) => {
    if (file) {
      setDetailUploadedFile(file);
    } else {
      setDetailUploadedFile(null);
      setDetailForm(prev => ({ ...prev, attachmentUrl: '' }));
    }
  };

  const resetForm = () => {
    setOrderForm({
      productId: '',
      teamCode: '',
      attachmentUrl: '',
      description: ''
    });
    setSelectedProduct('');
    setUploadedFile(null);
  };

  // 防
  const debouncedMutate = useMemo(
    () => debounce(createOrderMutation.mutateAsync, 700),
    [createOrderMutation.mutateAsync]
  )

  const handleSubmit = async () => {
    if (!selectedProduct) {
      toast.warning('请选择商品');
      return;
    }

    try {
      let attachmentUrl = '';
      if (uploadedFile) {
        const ossResult = await uploadFileToOSS(uploadedFile);
        attachmentUrl = ossResult;
      }

      const orderData = {
        productId: selectedProduct,
        teamCode: orderForm.teamCode,
        attachmentUrl,
        description: orderForm.description
      };
      debouncedMutate(orderData)

    } catch (error) {
      toast.dismiss();
      toast.error('上传图片失败，请重试');
    }
  };

  const handleUpdateOrder = async () => {
    if (!orderDetail) return;

    try {
      let attachmentUrl = detailForm.attachmentUrl;
      if (detailUploadedFile) {
        const ossResult = await uploadFileToOSS(detailUploadedFile);
        attachmentUrl = ossResult;
      }

      const updateData: UpdateOrderParams = {
        orderId: orderDetail._id,
        attachmentUrl,
        description: detailForm.description
      };
      updateOrderMutation.mutate(updateData);
    } catch (error) {
      toast.error('上传图片失败，请重试');
    }
  };

  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
  };

  const handleTeamCodeChange = (value: string) => {
    setOrderForm(prev => ({
      ...prev,
      teamCode: value
    }));
  };

  const handleInputChange = (field: keyof CreateOrderParams, value: string) => {
    setOrderForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleRefundOrder = (orderId: string) => {
    const refundOrderById: RefundOrderParams = {
      orderId,
    }
    refundOrderMutation.mutate(refundOrderById);
  }

  const handleViewOrder = (orderId: string) => {
    setCurrentOrderId(orderId);
    setDetailDialogOpen(true);
  };
  const selectedPackage = packages.find(pkg => pkg._id === selectedProduct);

  const getPayWay = (payWay: number): string => {
    switch (payWay) {
      case 1: return '后台充值';
      case 2: return '兑换码';
      case 3: return '小程序线上支付';
      case 4: return '权益包';
      case 5: return '极速算力赠送';
      default: return '';
    }
  };

  const getPayStatus = (payStatus: number): string => {
    switch (payStatus) {
      case 1: return '未支付';
      case 2: return '已支付';
      case 3: return '支付失败';
      default: return '';
    }
  };

  const columns: ColumnDef<OrderType>[] = [
    {
      header: '创建时间',
      cell: ({ row }) => (
        <span className="min-w-fit whitespace-nowrap pr-3">
          {row.original.createdAt
            ? formatDate(new Date(row.original.createdAt).getTime())
            : '--'}
        </span>
      ),
    },
    {
      header: '商品名称',
      cell: ({ row }) => <span >{row.original.productName}</span>,
    },
    {
      header: '包含算力',
      cell: ({ row }) => <span >{row.original.orderHashrate}</span>,
    },
    {
      header: '支付方式',
      cell: ({ row }) => <span>{getPayWay(row.original.payWay)}</span>,
    },
    {
      header: '团队名称/编号',
      cell: ({ row }) =>
        <div className='flex-col'>
          <span>{row.original.teamName}</span><br />
          <span className='text-blue-500 text-xs'>{row.original.teamCode}</span>
        </div>,
    },
    {
      header: '最后一次操作人',
      cell: ({ row }) => <span>{row.original.operateAdminName}</span>,
    },
    {
      header: '状态',
      cell: ({ row }) => <span className='max-w-[30px]'>{getPayStatus(row.original.payStatus)}</span>,
    },
    {
      header: '用户备注',
      cell: ({ row }) => <span >{row.original.description}</span>,
    },
    {
      header: '操作',
      cell: ({ row }) =>
        <div className='flex gap-2 text-blue-500 cursor-pointer'>
          <span className='pt-2' onClick={() => handleViewOrder(row.original._id)}>
            查看
          </span>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className='p-0'>
                更多
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => !row.original.isRefund && handleRefundOrder(row.original._id)}
                disabled={row.original.isRefund}
                className={row.original.isRefund ? 'opacity-50 cursor-not-allowed' : ''}
              >
                退款
                {row.original.isRefund && <span className="ml-2 text-xs">(已退款)</span>}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  const textarea = document.createElement('textarea');
                  textarea.value = row.original.orderSn;
                  document.body.appendChild(textarea);
                  textarea.select();
                  document.body.removeChild(textarea);
                  toast.success('复制成功！');
                }}
              >
                复制订单号
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
    },
  ];

  return (
    <div className="p-4">
      <SearchPlug
        packages={packages}
        onSearch={handleSearch}
        onReset={handleResetSearch}
      />
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogTrigger>
          <Button className='mb-4 ' onClick={() => setOpenDialog(true)}>创建订单</Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader className='border-b pb-2 font-semibold'>创建订单</DialogHeader>
          <div className='space-y-4  p-2 h-[78vh] '>
            <div className='flex'>
              <div className='w-[35%] flex h-full justify-end'>
                <img src={RequiredIco} className='w-[14px] h-[14px]' />
                <Label className='mb-0 p-3 pt-0'>商品</Label>
              </div>
              <div className="w-full space-y-2 h-[270px]">
                {isPackagesLoading ? (
                  <div className="text-center text-gray-500 py-4">加载中...</div>
                ) : (Array.isArray(packages) && packages.length > 0) ? (
                  packages.map(pkg => (
                    <div key={pkg._id} className="flex items-center gap-2">
                      <input
                        type="radio"
                        id={`product-${pkg._id}`}
                        name="product"
                        checked={selectedProduct === pkg._id}
                        onChange={() => handleProductSelect(pkg._id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 cursor-pointer border-gray-300 rounded-full"
                      />
                      <label htmlFor={`product-${pkg._id}`} className="text-sm cursor-pointer">
                        {pkg.productName} ({pkg.hashrate}算力)
                      </label>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500 py-4">暂无商品数据</div>
                )}
              </div>
            </div>

            <div className='flex '>
              <Label className='w-[35%] flex h-full justify-end items-end p-3 pt-6'>包含算力</Label>
              <div
                className="m-4 w-full text-sm mt-5 font-semibold"
              > {selectedPackage ? `${selectedPackage.hashrate}` : '请先选择商品'}</div>
            </div>

            <div className='flex items-center'>
              <div className='w-[35%] flex h-full justify-end'>
                <img src={RequiredIco} className='w-[14px] h-[14px]' />
                <Label className='mb-4 p-3 pt-0'>输入团队码</Label>
              </div>
              <div className='w-full mb-4 ml-2'>
                <Input
                  value={orderForm.teamCode}
                  onChange={(e) => handleTeamCodeChange(e.target.value)}
                  placeholder="请输入团队code"
                  className="mb-4 ml-2"
                />
              </div>
            </div>

            <div className='flex items-center'>
              <div className='w-[35%] flex h-full justify-end'>
                <Label className='mb-4 p-3 pt-0'>订单截图</Label>
              </div>
              <div className='w-full mb-4 ml-2'>
                <MediaUpload
                  onChange={handleImageUpload}
                  fileType='image'
                />
              </div>
            </div>

            <div className='flex mt-2'>
              <Label className='w-[35%] flex h-full justify-end items-end p-3'>订单用户备注</Label>
              <Input
                placeholder="请填写备注信息"
                className="mb-4"
                value={orderForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setOpenDialog(false);
              resetForm();
            }}>取消</Button>
            <Button
              onClick={handleSubmit}
              disabled={createOrderMutation.isPending}
              className={
                createOrderMutation.isPending
                  ? "opacity-50 cursor-not-allowed"
                  : "cursor-pointer"
              }
            >
              {createOrderMutation.isPending ? (
                <span className="flex items-center ">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin " />
                  创建中...
                </span>
              ) : (
                "确定"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 订单详情弹窗 */}
      <Dialog open={detailDialogOpen} onOpenChange={(open) => {
        setDetailDialogOpen(open);
        if (!open) setCurrentOrderId(null);
      }}>
        <DialogContent>
          <DialogHeader className='border-b pb-2 font-semibold'>详情</DialogHeader>
          <div className="p-4 space-y-4">

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">商品</Label>
              <div className="w-2/3">{orderDetail?.productName || '--'}</div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">包含算力</Label>
              <div className="w-2/3">{orderDetail?.orderHashrate || '--'}</div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">下单用户编号</Label>
              <div className="w-2/3">{orderDetail?.orderSn || '--'}</div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">下单人</Label>
              <div className="w-2/3">{orderDetail?.operateAdminName || '--'}</div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">下单渠道</Label>
              <div className="w-2/3">主团队</div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">订单截图</Label>
              <div className="w-2/3">
                <MediaUpload
                  onChange={handleDetailImageUpload}
                  fileType="image"
                  initialUrl={orderDetail?.attachmentUrl}
                />
              </div>
            </div>

            <div className="flex items-center">
              <Label className="w-1/3 text-right pr-4">订单用户备注</Label>
              <div className="w-2/3">
                <Input
                  value={detailForm?.description || ''}
                  onChange={(e) => setDetailForm({ ...detailForm, description: e.target.value })}
                />
              </div>
            </div>

            <div className="mt-4">
              <Label className="block mb-2">操作记录</Label>
              <div className="space-y-2">
                <div className="flex items-start">
                  <span className="w-4 h-4 rounded-full bg-gray-300 mt-1 mr-2"></span>
                  <div>
                    <div>创建订单</div>
                    <div className="text-sm text-gray-500">
                      {orderDetail?.createdAt ? formatDate(new Date(orderDetail.createdAt).getTime()) : '--'}
                      <span className="ml-2">处理人: {orderDetail?.operateAdminName || '--'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setDetailDialogOpen(false)}>取消</Button>
            <Button
              type="submit"
              onClick={handleUpdateOrder}
              disabled={updateOrderMutation.isPending}
            >确定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DataTable
        columns={columns}
        data={orderList?.data || []}
        pagination={pagination}
        setPagination={setPagination}
        rowCount={orderList?.totalSize}
      />
    </div>
  );
}