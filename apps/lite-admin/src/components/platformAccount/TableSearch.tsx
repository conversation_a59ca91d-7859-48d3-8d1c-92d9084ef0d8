import { useForm } from 'react-hook-form';
import { Button } from '@mono/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@mono/ui/form';
import { Input } from '@mono/ui/input';
import { isNil, omitBy } from 'lodash';
import { PlatformAccountParams } from '@/types/platformAccount';
import { CustomSelect } from '@mono/ui/common/CustomSelect';
import { loginStatus } from './columns';

type SearchForm = PlatformAccountParams;

export function TableSearch({
  values,
  onSearch,
}: {
  values?: PlatformAccountParams;
  onSearch: (values: PlatformAccountParams) => void;
}) {
  const form = useForm<SearchForm>({
    defaultValues: values,
  });

  function onSubmit(values: SearchForm) {
    const params: PlatformAccountParams = {
      teamName: values.teamName?.trim(),
      platformName: values.platformName?.trim(),
      platformAccountName: values.platformAccountName?.trim(),
      loginStatus: values.loginStatus,
    };
    const paramsOmitEmpty = omitBy(params, isNil);
    onSearch(paramsOmitEmpty);
  }
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-wrap gap-x-4 gap-y-2 p-0.5"
      >
        <FormField
          control={form.control}
          name="platformAccountName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>名称</FormLabel>
              <FormControl>
                <Input
                  className="w-40"
                  placeholder="账号名称/备注名称"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="teamName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="">团队</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="团队名称/id" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="platformName"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel>平台</FormLabel>
              <FormControl>
                <Input className="w-40" placeholder="平台名称" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="loginStatus"
          render={({ field }) => (
            <FormItem className="flex items-center gap-1 space-y-0">
              <FormLabel className="whitespace-nowrap">登录状态</FormLabel>
              <FormControl>
                <CustomSelect
                  options={loginStatus}
                  value={field.value}
                  onChange={field.onChange}
                  includeAll
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button type="submit">搜索</Button>
      </form>
    </Form>
  );
}
