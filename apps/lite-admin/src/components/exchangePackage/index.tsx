import { useState } from 'react';
import { PaginationState } from '@tanstack/react-table';
import { ExchangeCodeItem, ExchangePackageItem, GetExchangePackagesParams } from '@/types/exchangePackage';
import { addExchangePackage, fetchExchangePackageDetail, fetchExchangePackages, invalidExchangeCode, invalidExchangePackage } from '@/api/exchangePackage';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { Button } from '@mono/ui/button';
import { DataTable } from '@/components/dataTable';
import { getRedeemCodeColumn, getRedeemCodeDetailColumn } from './columns';
import { Input } from '@mono/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@mono/ui/select';
import { DialogTrigger, DialogContent, DialogHeader, Dialog, DialogFooter } from '@mono/ui/dialog';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { ScrollArea } from '@mono/ui/scroll-area';
import { fetchPowConsumList } from '@/api/settingPage/powPackage';

interface SearchParams {
  searchType: 1 | 2 | 3;
  searchText: string;
}

export function ExchangePackage() {
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [searchParams, setSearchParams] = useState<SearchParams>({
    searchType: 1,
    searchText: '',
  });
  const [activeSearchParams, setActiveSearchParams] = useState<SearchParams | null>(null); // 新增状态，记录当前生效的搜索条件

  const [code, setCode] = useState({
    packageName: '',
    productId: '',
    packageNum: 0,
  });

  const buildQueryParams = (): GetExchangePackagesParams => {
    const params: GetExchangePackagesParams = {
      page: pagination.pageIndex + 1,
      size: pagination.pageSize,
      searchType: activeSearchParams?.searchType ?? 1,
      searchText: activeSearchParams?.searchText?.trim() || '',
    };

    if (activeSearchParams && activeSearchParams.searchText.trim()) {
      params.searchType = activeSearchParams.searchType;
      params.searchText = activeSearchParams.searchText.trim();
    }

    return params;
  };


  const { data: response, refetch } = useQuery({
    queryKey: ['fetchExchangePackages', pagination, activeSearchParams],
    queryFn: () => fetchExchangePackages(buildQueryParams()),
  });

  const { data: packages = [] } = useQuery({
    queryKey: ['fetchPowConsumList'],
    queryFn: () => fetchPowConsumList({
      productName: '',
      hashrate: 0,
      price: 0
    }),
  });

  const total = response?.totalSize || 0;

  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [currentHashrate, setCurrentHashrate] = useState<number>(10000);//假设用户算力是10000，后需要传个值过来

  const [isAdding, setIsAdding] = useState(false); // 示例值
  //预计算力
  const selectedPackage = Array.isArray(packages)
    ? packages.find(pkg => pkg._id === selectedProduct)
    : undefined;
  const preDeductHashrate = selectedPackage ? selectedPackage.hashrate * Number(code.packageNum) : 0;
  const remainingHashrate = currentHashrate - preDeductHashrate;

  const [showDetail, setShowDetail] = useState(false); 
  // const [activeTab, setActiveTab] = useState('all'); // 当前选中标签
  const [selectedPackageDetail, setSelectedPackageDetail] = useState<ExchangePackageItem | null>(null);
  const [detailData, setDetailData] = useState<{
    packageInfo: ExchangePackageItem;
    detailList: ExchangeCodeItem[];
  } | null>(null);


  const handleDetailClick = async (pkg: ExchangePackageItem) => {
    try {
      setSelectedPackageDetail(pkg);

      const detailRes = await fetchExchangePackageDetail({
        packageId: pkg._id,
        searchType: 1,
        searchText: '',
        status: 0
      });

      setDetailData({
        packageInfo: pkg,
        detailList: detailRes?.data || []
      });

      setShowDetail(true);
    } catch (error) {
      toast.error('获取详情失败');
    }
  };

  const handleBackToList = () => {
    setShowDetail(false);
    setSelectedPackageDetail(null);
  };

  const saveCodeMutation = useMutation({
    mutationFn: addExchangePackage,
    onSuccess: () => {
      setCode({
        packageName: '',
        productId: '',
        packageNum: 0,
      });
      refetch();
      toast.success('兑换码包创建成功！');
      setIsAdding(false)
      setCurrentHashrate(10000)
      queryClient.invalidateQueries({ queryKey: ['fetchExchangePackages'] });
    },
    onError: (error) => {
      toast.error(`新增失败: ${error.message}`);
    }
  });

  const handleSearch = () => {
    if (!searchParams.searchText.trim()) {
      toast.warning('请输入搜索内容');
      return;
    }

    setActiveSearchParams(searchParams);
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  const handleReset = () => {
    setSearchParams({
      searchType: 1,
      searchText: '',
    });
    setActiveSearchParams(null);
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  const handleSubmit = () => {
    if (!code.packageName || !selectedProduct || code.packageNum <= 0) {
      toast.warning('请填写完整信息并选择兑换商品');
      return;
    }

    if (remainingHashrate < 0) {
      toast.error('当前算力不足，请重新设置兑换数量');
      return;
    }
    saveCodeMutation.mutate({
      packageName: code.packageName,
      productId: selectedProduct,
      packageNum: code.packageNum,
    });
  };

  const getPlaceholder = () => {
    switch (searchParams.searchType) {
      case 1: return '请输入兑换码';
      case 2: return '请输入兑换码名称';
      case 3: return '请输入创建人';
      default: return '请输入搜索内容';
    }
  };

  const invalidatePackageMutation = useMutation({
    mutationFn: invalidExchangePackage,
    onSuccess: () => {
      toast.success('兑换码包已作废');
      refetch();
      if (showDetail) {
        setShowDetail(false);
      }
    },
    onError: (error) => {
      toast.error(`作废失败: ${error.message}`);
    }
  });

  const invalidateCodeMutation = useMutation({
    mutationFn: invalidExchangeCode,
    onSuccess: () => {
      toast.success('兑换码已作废');
      if (showDetail && selectedPackageDetail) {
        fetchExchangePackageDetail({
          packageId: selectedPackageDetail._id,
          searchType: 1,
          searchText: '',
          status: 0
        }).then(res => {
          setDetailData(prev => ({
            ...prev!,
            detailList: res?.data || []
          }));
        });
      }
    },
    onError: (error) => {
      toast.error(`作废失败: ${error.message}`);
    }
  });

  const handleInvalidatePackage = (packageId: string) => {
    invalidatePackageMutation.mutate({ packageId });
  };

  const handleInvalidateCode = (codeId: string) => {
    invalidateCodeMutation.mutate({ codeId });
  };



  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      {showDetail && (
        <div>
          <div className="flex mt-8 pb-4  text-sm p-8">
            <div className='cursor-pointer text-gray-400' onClick={handleBackToList}>兑换码管理</div>
            <p className='ml-3 mr-3'>/</p>
            <div className='cursor-pointer'>兑换码详情</div>
          </div>

          <div className='w-full flex justify-between'>
            <div className='flex-col w-full gap-4 p-8'>
              <h2 className="text-lg font-semibold mb-3 border-l-4 border-green-500 pl-2">{selectedPackageDetail?.packageName}</h2>
              <div className='flex gap-16 '>
                <span className='text-gray-500'>商品</span>
                <span>{selectedPackageDetail?.hashrate}算力年包</span>
                <span className='text-gray-500'>创建人</span>
                <span>{selectedPackageDetail?.admin.name}</span>
              </div>
            </div>

            {/* 作废兑换码包api */}
            {/* <div className='w-[85px] bg-red-50 text-red-300 h-[45px] justify-center items-center flex'>作废</div> */}
          </div>
        </div>
      )}



      <div className="flex flex-col gap-4 p-4">

        <div className="p-4  rounded-lg shadow ">
          {showDetail ? (
            <h2 className="text-lg font-semibold mb-3 border-l-4 border-green-500 pl-2">兑换码详情</h2>
          ) : (
            <h2 className="text-lg font-semibold mb-3 p-6 pl-0">兑换码</h2>
          )}

          <Dialog open={isAdding} onOpenChange={setIsAdding}>
            {!showDetail && (
              <DialogTrigger>
                <Button>新增兑换码</Button>
              </DialogTrigger>
            )}


            {/* max-w-none：防止被外层视窗限制 */}
            <DialogContent className='w-[620px] max-w-none '>
              <DialogHeader className='p-3 mt-[-8px] border-b pt-0 border-gray-200 flex items-center'>添加兑换码</DialogHeader>


              <div className="flex flex-col md:grid-cols-3 gap-4 mb-3">
                <div className='flex '>
                  <img src={RequiredIco} className='w-[14px] h-[14px]' />
                  <label className="block w-[40%] text-sm font-medium mb-1">兑换码名称</label>
                  <Input
                    type="text"
                    className="w-full p-2 border rounded"
                    value={code.packageName}
                    onChange={(e) => setCode({ ...code, packageName: e.target.value })}
                  />
                </div>
                <div className='flex '>
                  <img src={RequiredIco} className='w-[14px] h-[14px]' />
                  <label className="block w-[40%] text-sm font-medium mb-1">兑换商品</label>
                  <div className="w-full space-y-2 h-[270px]">
                    {(Array.isArray(packages) && packages.length > 0) ? (
                      packages.map(pkg => (
                        <div key={pkg._id} className="flex items-center gap-2">
                          <input
                            type="radio"
                            id={`product-${pkg._id}`}
                            name="product"
                            checked={selectedProduct === pkg._id}
                            onChange={() => setSelectedProduct(pkg._id)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 cursor-pointer border-gray-300 rounded-full"
                          />
                          <label htmlFor={`product-${pkg._id}`} className="text-sm cursor-pointer">
                            {pkg.productName} ({pkg.hashrate}算力)
                          </label>
                        </div>
                      ))
                    ) : (<></>)}

                  </div>
                </div>

                {/* <div className='flex gap-5'>
                <label className="block  w-[40%] text-sm font-medium mb-1">最近使用过</label>
              </div> */}
                <div className='flex '>
                  <label className="block w-[32%] text-sm font-medium mb-1">包含算力</label>
                  <div>
                    {selectedProduct
                      ? `${packages.find(pkg => pkg._id === selectedProduct)?.hashrate || 0} 算力`
                      : "请先选择兑换商品"}
                  </div>
                </div>

                <div className='flex'>
                  <img src={RequiredIco} className='w-[14px] h-[14px]' />
                  <label className="block w-[30%] text-sm font-medium mb-1">兑换数量(1-200)</label>
                  <div className='flex-col'>
                    <Input
                      type="number"
                      min="1"
                      max="200"
                      className="w-[200px] p-2 border rounded"
                      value={code.packageNum}
                      onChange={(e) => setCode({ ...code, packageNum: +e.target.value })}
                    />
                    {remainingHashrate < 0 && (
                      <p className="text-red-500 text-xs mt-1">当前算力不足，请重新设置兑换数量</p>
                    )}
                  </div>
                </div>


                <div className='flex '>
                  <label className="block w-[30%] text-sm font-medium mb-1">当前账户算力</label>
                  <div>{currentHashrate}</div>
                </div>


                <div className='flex '>
                  <label className="block w-[30%] text-sm font-medium mb-1">预扣除算力</label>
                  <div>{preDeductHashrate}</div>
                </div>

                <div className='flex'>
                  <label className="block w-[30%] text-sm font-medium mb-1">剩余算力</label>
                  <div className={remainingHashrate < 0 ? 'text-red-500' : ''}>
                    {remainingHashrate}
                  </div>
                </div>

              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAdding(false)}>
                  取消
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={saveCodeMutation.isPending || remainingHashrate < 0}
                >
                  {saveCodeMutation.isPending ? '创建中...' : '确认创建'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>


        <div className="p-4  rounded-lg shadow">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">条件搜索</label>
              <Select
                value={String(searchParams.searchType)}
                onValueChange={(value) => {
                  setSearchParams(prev => ({
                    ...prev,
                    searchType: Number(value) as 1 | 2 | 3,
                  }));
                }}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="选择搜索类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">兑换码</SelectItem>
                  <SelectItem value="2">兑换码名称</SelectItem>
                  <SelectItem value="3">创建人</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 搜索输入框 */}
            <Input
              placeholder={getPlaceholder()}
              className="w-48"
              value={searchParams.searchText}
              onChange={(e) =>
                setSearchParams(prev => ({ ...prev, searchText: e.target.value }))
              }
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />


            <Button
              onClick={handleSearch}
            >
              搜索
            </Button>

            <Button
              variant="outline"
              onClick={handleReset}
            >
              重置
            </Button>
          </div>
        </div>

        {showDetail && (
          <div className="flex border-b mb-4">
            {['全部', '未兑换(0)', '已兑换(0)', '已作废'].map((tab) => (
              <button
                key={tab}
                className={`px-4 py-2 text-sm ${tab === '全部' ? 'border-b-2 border-green-500 text-green-500' : 'text-gray-500'}`}
              >
                {tab}
              </button>
            ))}
          </div>
        )}

        {/* 表格区域 */}
        <div className=" rounded-lg shadow overflow-hidden">
          {!showDetail && (
            <DataTable
              columns={getRedeemCodeColumn(
                handleDetailClick,
                handleInvalidatePackage,
              )}
              data={response?.data}
              rowCount={total}
              pagination={pagination}
              setPagination={setPagination}
            />
          )}

          {showDetail && detailData && (
            <DataTable
              columns={getRedeemCodeDetailColumn(
                handleInvalidateCode,
              )}
              data={detailData?.detailList || []}
              rowCount={detailData?.detailList.length || 0}
              pagination={pagination}
              setPagination={setPagination}
            />

          )}
        </div>

      </div>
    </ScrollArea>
  );
}