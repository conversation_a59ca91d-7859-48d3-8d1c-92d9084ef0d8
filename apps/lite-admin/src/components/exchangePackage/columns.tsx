import { ColumnDef } from '@tanstack/react-table';
import { formatDate } from '@mono/utils/day';
import { ExchangeCodeItem, ExchangePackageItem } from '@/types/exchangePackage';
import { toast } from 'sonner';

export const getRedeemCodeColumn = (
  handleDetailClick: (pkg: ExchangePackageItem) => void,
  handleInvalidatePackage: (packageId: string) => void,
): Array<ColumnDef<ExchangePackageItem>> => [
    {
      header: '兑换码名称',
      cell: ({ row }) => (
        <span>{row.original.packageName}</span>
      ),
    },
    {
      header: '兑换商品',
      cell: ({ row }) => (
        <span>{row.original.hashrate}算力年包</span>
      ),
    },
    {
      header: '已兑换/总数',
      cell: ({ row }) => (
        <span>{row.original.usePackageNum}/{row.original.packageNum}</span>
      ),
    },
    {
      header: '创建时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => (
        <span>
          {row.original.createdAt ?
            formatDate(new Date(row.original.createdAt).getTime()) :
            '--'}
        </span>
      ),
    },
    {
      header: '创建人',
      cell: ({ row }) => (
        <span>{row.original.admin.name}</span>
      ),
    },
    {
      header: '操作',
      cell: ({ row }) => {
        const isInvalidated = row.original.status === 0; //兑换码包的返回值中没有状态，所以并不知道到底根据哪个字段来决定是否已作废
        return (
          <div className={`${isInvalidated ? 'text-blue-300' : 'text-blue-500'} cursor-pointer`}>
            <span
              className='mr-3 hover:underline'
              onClick={() => handleDetailClick(row.original)}
            >
              详情
            </span>
            <span
              className={`hover:underline ${isInvalidated ? 'cursor-not-allowed' : ''}`}
              onClick={() => !isInvalidated && handleInvalidatePackage(row.original._id)}
            >
              作废
            </span>
          </div>
        );
      },
    }
  ];


export const getRedeemCodeDetailColumn = (
  handleInvalidateCode: (codeId: string) => void,
): Array<ColumnDef<ExchangeCodeItem>> => [
    {
      header: '兑换码',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <span>{row.original.code}</span>
          <button
            onClick={() => {
              const textarea = document.createElement('textarea');
              textarea.value = row.original.code;
              document.body.appendChild(textarea);
              textarea.select();
              document.body.removeChild(textarea);
              toast.success('复制成功！')
            }}
            // onClick={() => {
            //   navigator.clipboard.writeText(
            //     `${import.meta.env.VITE_APP_OFFICIAL_URL}?${row.original.code}`
            //   )
            //   toast.success('复制成功！')
            // }}
            className="text-gray-400 hover:text-blue-500 ml-1"
            title="复制"
          >
            ⎘
          </button>
        </div>
      ),
    },
    {
      header: '状态',
      cell: ({ row }) => {
        const status = row.original.status === 0
          ? '已作废'
          : (row.original.status === 1
            ? '未兑换'
            : (row.original.status === 2
              ? '已兑换'
              : '未知状态'));

        // 根据状态决定样式
        let dotColor = '';
        let textColor = '';

        switch (row.original.status) {
          case 0:
            dotColor = 'text-gray-300';
            textColor = 'text-gray-400';
            break;
          case 1:
            dotColor = 'text-blue-500';
            textColor = '';
            break;
          case 2:
            dotColor = 'text-green-500';
            textColor = 'text-green-500';
            break;
          default:
            dotColor = 'text-gray-500';
            textColor = 'text-gray-500';
        }

        return (
          <div className="flex items-center">
            <span className={`mr-1 scale-150 ${dotColor}`}>•</span>
            <span className={textColor}>{status}</span>
          </div>
        );
      },
    },
    {
      header: '兑换时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => (
        <span>
          {row.original.createdAt ?
            formatDate(new Date(row.original.updatedAt).getTime()) :
            '--'}
        </span>
      ),
    },
    {
      header: '订单号',
      cell: ({ row }) => (
        <span>{row.original.orderSn || '--'}</span>
      ),
    },
    {
      header: '兑换人',
      cell: ({ row }) => (
        <span>{row.original.userId || '--'}</span>
      ),
    },
    {
      header: '用户备注',
      cell: ({ row }) => (
        <span>{row.original.exchangedAt || '--'}</span>
      ),
    },
    {
      header: '操作',
      cell: ({ row }) => {
        const isInvalidated = row.original.status === 0; // 0表示已作废
        return (
          <div className={isInvalidated ? 'text-blue-300' : 'text-blue-500'}>
            <span
              className={`hover:underline ${isInvalidated ? 'cursor-not-allowed' : 'cursor-pointer'}`}
              onClick={() => !isInvalidated && handleInvalidateCode(row.original._id)}
            >
              作废
            </span>
          </div>
        );
      },
    }
  ];