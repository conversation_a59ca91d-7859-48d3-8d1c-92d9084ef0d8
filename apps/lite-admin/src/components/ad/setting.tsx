import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@mono/ui/dialog';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mono/ui/form';
import { LoadingButton } from '@/components/loading-button';
import { SetStateAction, useEffect, useMemo, useState } from 'react';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { RadioGroup, RadioGroupItem } from '@mono/ui/radio-group';
import { Textarea } from '@mono/ui/textarea';
import { AdDto } from '@/types/ad';
import { Switch } from '@mono/ui/switch';
import { Popover, PopoverContent, PopoverTrigger } from '@mono/ui/popover';
import { Calendar } from '@mono/ui/calendar';
import { cn } from '@/lib/utils';
import { DateRange } from 'react-day-picker';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { addAdConfig, setAdConfig } from '@/api/ad';
import { Loader2 } from 'lucide-react';

export function AdSetting({
  ad,
  open,
  setOpen,
}: {
  ad?: AdDto;
  open: boolean;
  setOpen: (open: boolean) => void;
}) {
  const queryClient = useQueryClient();
  const [isPopoverOpen, setPopoverOpen] = useState(false);
  const [date, setDate] = useState<DateRange | undefined>();
  const [isTrueSelected, setIsTrueSelected] = useState(false); // 用来控制是否显示
  const [imageUrl, setImageUrl] = useState<string | null>(null); // 存储图片的URL

  const formSchema = useMemo(() => {
    return z.object({
      id: z.string().optional(),
      name: z.string().min(1).max(15, '最多15个字'),
      adUrl: ad?.id ? z.string().optional() : z.string().min(1),
      enabled: z.boolean(),
      isTimed: z.boolean(),
      sort: z.number().min(1),
      expiredStartAt: z.number().optional(),
      expiredEndAt: z.number().optional(),
      isJumpTo: z.boolean(),
      jumpToUrl: z.string().optional(),
    });
  }, [ad]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      name: '',
      enabled: true,
      sort: 1,
      adUrl: '',
      isTimed: true,
      expiredStartAt: 0,
      expiredEndAt: 0,
      isJumpTo: false,
      jumpToUrl: '',
    },
  });

  // 日期选择回调
  const handleDateSelect = (
    selectedDate: SetStateAction<DateRange | undefined>
  ) => {
    const startTime =
      selectedDate && 'from' in selectedDate && selectedDate.from ?
        new Date(new Date(selectedDate.from).setHours(0, 0, 0, 0)).getTime()
      : undefined;
    const endTime =
      selectedDate && 'to' in selectedDate && selectedDate.to ?
        new Date(new Date(selectedDate.to).setHours(23, 59, 59, 0)).getTime()
      : undefined;
    setDate(selectedDate); // 设置选择的日期
    form.setValue('expiredStartAt', startTime);
    form.setValue('expiredEndAt', endTime);
  };

  useEffect(() => {
    if (open && ad) {
      form.setValue('id', ad.id);
      form.setValue('name', ad.name);
      form.setValue('sort', ad.sort);
      form.setValue('enabled', ad.enabled);
      form.setValue('isTimed', ad.isTimed);
      form.setValue('expiredStartAt', ad.expiredStartAt);
      form.setValue('expiredEndAt', ad.expiredEndAt);
      form.setValue('isJumpTo', ad.isJumpTo);
      form.setValue('jumpToUrl', ad.jumpToUrl);

      if (ad.adPath) {
        setImageUrl(ad.adPath);
      }
      if (ad.expiredStartAt > 0 && ad.expiredEndAt > 0) {
        setDate({
          from: new Date(ad.expiredStartAt),
          to: new Date(ad.expiredEndAt),
        });
      }
      if (ad.isTimed !== undefined) {
        setIsTrueSelected(ad.isTimed);
      }
    } else {
      setImageUrl(null);
    }
  }, [form, ad, open]);

  const mutation = useMutation({
    mutationFn: addAdConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['adList'],
      });
    },
  });

  const editMutation = useMutation({
    mutationFn: setAdConfig,
    onSuccess: () => {
      setOpen(false);
      form.reset();
      queryClient.refetchQueries({
        queryKey: ['adList'],
      });
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      return;
    }
    const url = URL.createObjectURL(file); // 创建图片的临时URL
    setImageUrl(url);

    const reader = new FileReader();
    reader.onloadend = () => {
      const base64String = reader.result?.toString() ?? '';
      form.setValue('adUrl', base64String.split(',')[1]);
      form.trigger('adUrl').then();
    };
    reader.readAsDataURL(file);
  };

  const handleRemoveImage = () => {
    setImageUrl('');
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (values.id) {
      editMutation.mutate({
        id: values.id,
        name: values.name,
        sort: values.sort,
        adUrl: values.adUrl,
        enabled: values.enabled,
        isJumpTo: values.isJumpTo,
        isTimed: values.isTimed,
        jumpToUrl: values.jumpToUrl,
        expiredStartAt: values.expiredStartAt,
        expiredEndAt: values.expiredEndAt,
      });
    } else {
      mutation.mutate({
        name: values.name,
        sort: values.sort,
        adUrl: values.adUrl,
        enabled: values.enabled,
        isJumpTo: values.isJumpTo,
        isTimed: values.isTimed,
        jumpToUrl: values.jumpToUrl,
        expiredStartAt: values.expiredStartAt,
        expiredEndAt: values.expiredEndAt,
      });
    }
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <DialogHeader>
              <DialogTitle>{ad?.id ? '修改' : '添加'}banner</DialogTitle>
              <VisuallyHidden>
                <DialogDescription />
              </VisuallyHidden>
            </DialogHeader>

            <div className="flex flex-col mt-6 mb-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      名称:
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="flex-grow"
                        placeholder="填写名称"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="sort"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      排序：
                    </FormLabel>
                    <FormControl>
                      <div>
                        <Input
                          type="number"
                          placeholder="请输入"
                          min={1}
                          {...field}
                          onChange={(e) => {
                            const value =
                              e.target.value ?
                                parseInt(e.target.value, 10)
                              : '';
                            field.onChange(value);
                          }}
                        />
                        <FormMessage />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="adUrl"
                render={() => (
                  <FormItem className="gap-1 mb-4 mt-4 space-y-2">
                    <FormLabel className="w-full text-right mb-2">
                      广告图：
                      <span className=" text-muted-foreground">
                        请上传424px*118px大小图片
                      </span>
                    </FormLabel>
                    <FormControl className="w-full">
                      {!imageUrl ?
                        // 显示上传按钮
                        <Input
                          type="file"
                          accept={'.jpg,.png,.jpeg'}
                          className="w-full"
                          onChange={(e) => handleFileChange(e)}
                        />
                      : <div className="flex flex-col space-x-4">
                          <img
                            src={imageUrl}
                            alt="Uploaded preview"
                            className="w-48 h-24 object-cover mb-2"
                            style={{ width: '423px', height: '118px' }}
                          />
                          <button
                            type="button"
                            onClick={handleRemoveImage}
                            className="px-4 py-2 bg-red-500 text-white rounded"
                            style={{ width: '80px', margin: '0' }}
                          >
                            删除
                          </button>
                        </div>
                      }
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="enabled"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      是否上架：
                    </FormLabel>
                    <FormControl>
                      <div>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <FormMessage />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isTimed"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      有效期:
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        value={String(field.value)}
                        onValueChange={(value: string) => {
                          field.onChange(value === 'true');
                          setIsTrueSelected(value === 'true');
                        }}
                        className="flex flex-row space-x-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="false" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            永久有效
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="true" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            设置时间
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                  </FormItem>
                )}
              />

              {isTrueSelected && (
                <FormField
                  control={form.control}
                  name="expiredEndAt"
                  render={() => (
                    <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                      <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                        有效时间：
                      </FormLabel>
                      <FormControl>
                        <Popover
                          open={isPopoverOpen}
                          onOpenChange={setPopoverOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              id="date"
                              variant={'outline'}
                              className={cn(
                                'w-[250px] justify-start text-left font-normal',
                                !date && 'text-muted-foreground'
                              )}
                              onClick={() => setPopoverOpen(true)} // 打开日历
                            >
                              {date?.from ?
                                date.to ?
                                  <>
                                    {format(date.from, 'yyyy-MM-dd')} -{' '}
                                    {format(date.to, 'yyyy-MM-dd')}
                                  </>
                                : format(date.from, 'yyyy-MM-dd')
                              : <span>选择日期</span>}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              initialFocus
                              mode="range"
                              defaultMonth={date?.from}
                              selected={date}
                              onSelect={handleDateSelect}
                              numberOfMonths={2}
                              locale={zhCN}
                            />
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="isJumpTo"
                render={({ field }) => (
                  <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                    <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                      是否跳转：
                    </FormLabel>
                    <FormControl>
                      <div>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <FormMessage />
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
              {form.getValues('isJumpTo') === true && (
                <FormField
                  control={form.control}
                  name="jumpToUrl"
                  render={({ field }) => (
                    <FormItem className="flex items-center gap-1 mb-4 mt-4 space-y-0">
                      <FormLabel className="w-50 flex-shrink-0 text-right mr-2">
                        跳转地址:
                      </FormLabel>
                      <FormControl>
                        <Textarea placeholder="填写跳转地址" {...field} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              )}
            </div>

            <DialogFooter>
              <Button
                onClick={() => {
                  form.reset();
                  setOpen(false);
                }}
                variant="ghost"
                type="reset"
              >
                取消
              </Button>
              <LoadingButton
                isPending={mutation.isPending}
                disabled={mutation.isPending}
                type="submit"
                className='hover:bg-primary/90'
              >
                {mutation.isPending&&(<Loader2 className='h-4 w-4 animate-spin'/>)}
                保存
              </LoadingButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
