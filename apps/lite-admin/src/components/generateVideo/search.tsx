import { useState } from "react";
import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { Input } from "@mono/ui/input";
import { Button } from "@mono/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@mono/ui/select";
import { Label } from "@mono/ui/label";

export function SearchBar({
  onSearch,
  onReset,
}: {
  onSearch: (params: {
    searchType: string;
    searchText: string;
    dateRange: DateRange | undefined;
  }) => void;
  onReset: () => void;
}) {
  const [searchType, setSearchType] = useState("1"); 
  const [searchText, setSearchText] = useState("");
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const handleSearch = () => {
    onSearch({
      searchType,
      searchText,
      dateRange,
    });
  };

  const handleReset = () => {
    setSearchType("1");
    setSearchText("");
    setDateRange(undefined);
    onReset();
  };

  return (
    <div className="flex flex-wrap items-end gap-4 p-4 border rounded-lg mb-4">
      <div className="flex items-center gap-2">
        <Select value={searchType} onValueChange={setSearchType}>
          <Label>条件搜索</Label>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="搜索类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">用户手机号</SelectItem>
            <SelectItem value="2">视频ID</SelectItem>
          </SelectContent>
        </Select>
        <Input
          placeholder={
            searchType === "1"
              ? "请输入用户手机号"
              : "请输入视频ID"
          }
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="w-[200px]"
        />
      </div>


      <div className="flex items-center gap-2">
        <span className="text-sm">提交时间</span>
        <DatePickerWithRange
          className="w-[250px]"
          onChange={setDateRange}
          value={dateRange}
        />
      </div>

      <div className="flex gap-2 ml-auto">
        <Button variant="outline" onClick={handleReset}>
          重置
        </Button>
        <Button onClick={handleSearch}>搜索</Button>
      </div>
    </div>
  );
}