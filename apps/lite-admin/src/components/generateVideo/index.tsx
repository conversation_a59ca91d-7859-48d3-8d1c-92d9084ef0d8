import { fetchCourseVideo } from "@/api/generateVideo";
import { VideoCreationItem, VideoCreationResponse } from "@/types/generateVideo";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
import { useState } from "react";
import { DataTable } from "../dataTable";
import { ScrollArea } from "@mono/ui/scroll-area";
import { Button } from "@mono/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import { SearchBar } from './search'

interface SearchParams {
  page: number;
  size: number;
  searchType: string;
  searchText: string;
  startDate?: number;  // 可选的时间戳
  endDate?: number;    // 可选的时间戳
  type?: string;
  channel?: string;
}

export function GenerateVideoIndex() {

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 1,
    pageSize: 10,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({
    page: 1,
    size: 10,
    searchType: '1',
    searchText: '15574920253',
  });
  const queryClient = useQueryClient();

  const { data: VideoList } = useQuery<VideoCreationResponse>({
    queryKey: ['fetchCourseVideo', searchParams],
    // queryFn: () => fetchCourseVideo({})
    queryFn: () => fetchCourseVideo({
      ...searchParams,
      ...(searchParams.startDate && { startDate: searchParams.startDate }),
      ...(searchParams.endDate && { endDate: searchParams.endDate })
    })
  })

  const [open, setOpen] = useState(false)
  const VideoContentList: ColumnDef<VideoCreationItem>[] = [
    {
      header: '封面',
      accessorKey: 'thumbnailUrl',
      cell: ({ row }) => <span>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger >
            <img src={row.original.thumbnailUrl} className="w-14 cursor-pointer relative" />
            <DialogContent className=" flex flex-col items-center justify-center p-6">
              <DialogHeader >素材预览</DialogHeader>
              <div className=" w-full flex items-center justify-center">
                <video
                  src={row.original.fileUrl}
                  className="max-w-full h-[50vh] object-contain"
                  controls
                />
              </div>
            </DialogContent>
          </DialogTrigger>
        </Dialog>
      </span>,
    },
    {
      header: '用户手机号',
      accessorKey: 'userPhone',
    },
    {
      header: '提交时间',
      accessorKey: 'createdAt',
    },
    {
      header: '备注',
      accessorKey: 'text',
      cell: ({ row }) => (
        <div className="max-h-20 overflow-y-auto text-sm">
          {row.original.text}
        </div>
      ),
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) =>
        <div className="flex items-center">
          <span
            className={`mr-1 text-lg  ${row.original.status === 'succeed' ? 'text-green-500' : 'text-red-500'}`}
          >
            •
          </span>
          <span>
            {row.original.status === 'succeed' ? '训练成功' : '训练失败'}
          </span>
        </div>
    },
    {
      header: '操作',
      accessorKey: 'description',
      cell: () =>
        <Button variant="link" className="text-blue-500 p-0">
          复制ID
        </Button>
    },
  ];

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="flex flex-col gap-2 overflow-hidden">

        <h1 className="text-xl font-bold mb-4">生成视频</h1>


        <SearchBar
          onSearch={(params) => {
            setSearchParams({
              ...searchParams,
              searchType: params.searchType,
              searchText: params.searchText,
              ...(params.dateRange?.from && {
                startDate: params.dateRange.from.getTime()
              }),
              ...(params.dateRange?.to && {
                endDate: params.dateRange.to.getTime()
              }),
            });
            queryClient.invalidateQueries({ queryKey: ['fetchCourseVideo'] });
          }}
          onReset={() => {
            setSearchParams({
              page: 1,
              size: 10,
              searchType: '1',
              searchText: '',
              startDate: undefined,
              endDate: undefined,
            });
            queryClient.invalidateQueries({ queryKey: ['fetchCourseVideo'] });
          }}
        />


        <div>
          <DataTable
            columns={VideoContentList}
            data={VideoList?.data || []}
            rowCount={VideoList?.totalSize}
            pagination={{
              pageIndex: pagination.pageIndex - 1,
              pageSize: pagination.pageSize,
            }}
            setPagination={(updater) => {
              const newPagination = typeof updater === 'function'
                ? updater({ pageIndex: searchParams.page - 1, pageSize: searchParams.size })
                : updater;

              setSearchParams(prev => ({
                ...prev,
                page: newPagination.pageIndex + 1,
                size: newPagination.pageSize
              }));
            }}
          />
        </div>

      </div>
    </ScrollArea>
  );
}
