import { <PERSON>rollArea } from '@mono/ui/scroll-area';
import { AddChannelCom } from './add-showCom'
import { ShowCom } from './add-showCom'
import { fetchChannelList, addChannelCom } from '@/api/channelManage';
import { FetchChannelParams, } from '@/types/channelManage';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useState } from 'react';
// import { boolean, number } from 'zod';
import { SearchCom } from "./searchCom";
import { ComputingCom } from './computeCom'
import { LoadingContainer } from '../loading';

export function MainComponent() {
  const queryParams = {
    searchType: 1,
    searchText: '',
    startDate: null,
    endDate: null,
  };


  const queryClient = useQueryClient();

  const [searchParams, setSearchParams] = useState<FetchChannelParams>({
    searchType: 1,
    searchText: undefined,
    startDate: undefined,
    endDate: undefined
  });

  const { data, isLoading } = useQuery({
    queryKey: ['fetchChannelList', queryParams],
    queryFn: async () => {
      const res = await fetchChannelList(queryParams); // 不传 searchText
      return res || [];
    },
  });
  console.log(data);
  

  const handleSearch = (params: FetchChannelParams) => {
    setSearchParams(params);
  };



  const saveMutation = useMutation({
    mutationFn: addChannelCom,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchChannelList'] });
      toast.success('新建成功');
    },
    onError: (error) => {
      toast.error(`新建失败: ${error.message}`);
    }
  });


  const handleAddChannel = (name: string) => {
    const newChannel = {
      channelName: name,
      coverUrl: 'https://placeholder.im/200x200/No_image/f7faf9/b4b6b5',
      description: '描述',
      channelType: '分类一',
      impower: '0',
      isRecommend: 'false',//布尔值字符，是否推荐，默认不推荐
      status: '1',//0 隐藏 1显示，创建时默认1显示
    };
    saveMutation.mutate(newChannel);
  };


  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className='pl-12 pt-3'>
        <div className=" pt-4">
          <div className="text-2xl font-bold pt-4">频道管理</div>
        </div>
        <div className="flex justify-between items-center pr-9">
          <div><AddChannelCom onAddChannel={handleAddChannel} /></div>
          <div><ComputingCom /></div>
        </div>

        <div className=" pt-0">
          <SearchCom onSearch={handleSearch} initialValues={searchParams} />

        </div>
        {isLoading ? (<div className="fixed inset-0 flex items-center justify-center  z-50">
          <LoadingContainer />
        </div>) : (
          <div className=" pt-4">
            <ShowCom searchParams={searchParams} />
          </div>
        )}

      </div>
    </ScrollArea>
  );
}