import { Progress } from '@mono/ui/progress';


export function ComputingCom() {
  // 替换成动态传入的数据
  const totalCompute = 100;
  const usedCompute = 0.1;

  return (
      <div className="pl-2 pt-2">
      
        <div className="w-[290px] h-[90px] flex flex-col justify-between rounded-md border p-4 pt-1.5">
        
          <div className="flex  items-center h-[80px] ml-1.5 mt-[-4px]">
            <div className="font-semibold text-sm">{usedCompute}GB/{totalCompute}GB</div>
            <div className="text-xs ml-8 mt-1 text-gray-500 ">已用量 / 总容量</div>
          </div>

       
          <div className="h-[30px] flex items-center justify-center mt-[-18px]">
            <div className="w-[95%]">
              <Progress className="h-1.5" value={(usedCompute / totalCompute) * 100} />
            </div>
          </div>

        
          <div className="h-[30px] flex text-xs gap-12  ml-2 mt-[4px] mb-[-9px] ">
            <div><span className='text-blue-400'>●</span> 文案{usedCompute}GB</div>
            <div><span className='text-green-400'>●</span> 素材{usedCompute}GB</div>
          </div>

        </div>
      </div>
  );
}