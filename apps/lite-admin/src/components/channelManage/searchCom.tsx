import { Button } from '@mono/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@mono/ui/form';
import { FetchChannelParams } from '@/types/channelManage';
import { DateRange } from 'react-day-picker';
import { DatePickerWithRange } from '../DatePickerWithRange';
import { Input } from '@mono/ui/input';
import { CustomSelect } from '@mono/ui/common/CustomSelect';
// import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { format, } from 'date-fns';
import { isUndefined, omitBy } from 'lodash';


// 定义类型
export type SearchComProps = {
  parentPage?: 'channel' | 'user' | 'order';
  initialValues?: FetchChannelParams;
  onSearch: (values: FetchChannelParams) => void;
  // onSearch: (values: FetchChannelParams) => void;
  searchTypeOptions?: Array<{ value: number; label: string }>;
};

export function SearchCom({
  parentPage = 'channel', // 默认值
  initialValues,
  onSearch,
  searchTypeOptions = [ // 默认选项
    { value: 1, label: '频道名称' },
    { value: 2, label: '频道号' }
  ],
}: SearchComProps) {
  const form = useForm({
    defaultValues: {
      searchType: undefined,
      searchText: '',
      startDate: '',
      endDate: '',
      ...initialValues,
    },
  });

  const handleDateChange = (range: DateRange) => {
    const currentDate = new Date(new Date().setHours(0, 0, 0, 0));
    let to = range.to;
    if (to && to > currentDate) to = currentDate;
    form.setValue('startDate', range.from ? format(range.from, 'yyyy-MM-dd') : '');
    form.setValue('endDate', to ? format(to, 'yyyy-MM-dd') : '');
  };

  const onSubmit = (formData: FetchChannelParams) => {
    const processedData = omitBy({
      ...formData,
      startDate: formData.startDate
        ? new Date(formData.startDate).getTime()
        : undefined,
      endDate: formData.endDate
        ? new Date(formData.endDate).getTime() + 23 * 59 * 59 * 1000 // 结束时间+1天
        : undefined,
    }, isUndefined);

    onSearch(processedData);
  };


  // 根据父组件定制
  const getPlaceholder = () => {
    switch (parentPage) {
      case 'user': return '请输入用户名';
      case 'order': return '请输入订单号';
      default: return '请输入';
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-wrap gap-4 p-2">
        <FormField
          name="searchType"
          render={({ field }) => (
            <FormItem className="flex items-center gap-3 space-y-4">
              <FormLabel className="whitespace-nowrap mt-5 block">条件搜索</FormLabel>
              <FormControl>
                <CustomSelect
                  options={searchTypeOptions}
                  value={field.value}
                  onChange={field.onChange}
                  className="w-32 "
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          name="searchText"
          render={({ field }) => (
            <FormItem className="flex items-center gap-3 space-y-4">
              <FormControl >
                <Input
                  placeholder={getPlaceholder()}
                  className="w-40 mt-4"
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          name="dateRange"
          render={({ field }) => (
            <FormItem className="flex flex-col sm:flex-row items-start sm:items-center gap-3 space-y-2 sm:space-y-0">
              <FormLabel className='whitespace-nowrap block mt-4 ml-6'>创建时间</FormLabel>
              <FormControl>
                <DatePickerWithRange
                  value={{
                    from: field.value?.from,
                    to: field.value?.to,
                  }}
                  onChange={(range) => {
                    field.onChange(range); 
                    handleDateChange(range); 
                  }}
                  maxDate={new Date()}
                  className="w-[250px] !mt-4"
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* 根据父组件定制 */}
        <FormItem className="items-center space-x-3 pl-4 mt-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              form.reset({
                searchType: undefined,
                searchText: '',
                startDate: '',
                endDate: '',
              });

              // 全量搜索，要是没要求searchType的话，这里可以不用写的
              onSearch({
                searchType: 1 || 2,
              });
            }}
          >
            重置
          </Button>
          <Button type="submit">搜索</Button>
        </FormItem>
      </form>
    </Form>
  );
}