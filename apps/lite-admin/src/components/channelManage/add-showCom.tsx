import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogTrigger } from '@mono/ui/dialog';
// import { ScrollArea } from '@mono/ui/scroll-area';
import { Button } from '@mono/ui/button';
import { LoadingButton } from '../loading-button';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Input } from '@mono/ui/input';
import { Label } from '@mono/ui/label';
// import { DropdownMenu } from '@mono/ui/dropdown-menu';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@mono/ui/dropdown-menu'
import { useState } from 'react';
import { channelDetailList, FetchChannelParams } from '@/types/channelManage';
import { fetchChannelList, delChannel, completeChannel } from '@/api/channelManage';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { DataTable } from "@/components/dataTable";
import { ColumnDef } from "@tanstack/react-table";
import { useMutation, } from "@tanstack/react-query";
import { createAdminUser, fetchAdminUser, removeAdminUser } from "@/api/channelManage";
import { AdminUserList, CreateAdminUser } from '@/types/channelManage';

type ShowComProps = {
  searchParams: FetchChannelParams;
};

type AddChannelComProps = {
  onAddChannel: (channelName: string) => void;
};
export function AddChannelCom({ onAddChannel }: AddChannelComProps) {
  const [channelName, setChannelName] = useState('');
  const [isComposing, setIsComposing] = useState(false);
  const [open, setOpen] = useState(false);

  const handleConfirm = () => {
    if (!channelName.trim()) {
      toast.warning('请输入频道名称')
      return;
    }
    onAddChannel(channelName);
    setOpen(false);
    setChannelName('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    if (!isComposing) {
      setChannelName(value.slice(0, 10));
    } else {
      setChannelName(value);
    }
  };

  const handleCompositionStart = () => {
    setIsComposing(true);

  };

  const handleCompositionEnd = (e: React.CompositionEvent<HTMLInputElement>) => {
    setIsComposing(false);
    const value = e.currentTarget.value;
    setChannelName(value.slice(0, 10));
  };

  return (
    <div className="pt-8 ">

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button >
            新建频道
          </Button>
        </DialogTrigger>
        <DialogContent className="!max-w-none w-[600px]  p-6">
          <DialogHeader className=" pb-4 mb-4 w-full items-center border-b justify-center text-center">
            <DialogTitle>新建频道</DialogTitle>
          </DialogHeader>

          <div className="flex flex-col gap-2 m-4 ml-6 mt-4 pb-4 ">
            <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
              <img src={RequiredIco} className="w-[14px] h-[14px]" />
              <Label className="text-[14px] w-16">
                频道名称
              </Label>
              <div className="w-[340px] ml-4 mt-[-6px]">
                <Input
                  value={channelName}
                  onChange={handleInputChange}
                  onCompositionStart={handleCompositionStart}
                  onCompositionEnd={handleCompositionEnd}
                  className="w-full"
                  placeholder="请输入十位内频道名称"
                />

                <span></span>
              </div>
            </div>
          </div>

          <div className='w-full'>
            <DialogFooter className="border-t pt-4">
              <Button className="border border-gray-300" variant="ghost" onClick={() => setOpen(false)}>
                取消
              </Button>
              <LoadingButton onClick={handleConfirm}>继续</LoadingButton>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export function ShowCom({ searchParams }: ShowComProps) {
  const queryClient = useQueryClient();
  const [openDialog, setOpenDialog] = useState(false); // 控制一层弹出框开关
  const [selectedChannelId, setSelectedChannelId] = useState<string>(''); // 当前点击的频道 ID

  const { data } = useQuery({
    queryKey: ['fetchChannelList', searchParams],
    queryFn: async () => fetchChannelList(searchParams),
  });

  const handleOpenAdminDialog = (channelId: string) => {
    setSelectedChannelId(channelId);
    setOpenDialog(true);
  };

  const handleDelete = async (_id: string) => {
    try {
      await delChannel({ _id });
      toast.success('删除成功');
      queryClient.invalidateQueries({ queryKey: ['fetchChannelList'] });

    } catch (error) {
      toast.error('删除失败');
    }
  };

  const handleToggleStatus = async (item: channelDetailList) => {
    try {
      const updatedStatus = item.status === 1 ? 0 : 1;

      const payload = {
        channelName: item.channelName || '',
        coverUrl: item.coverUrl || '',
        description: item.description || '',
        channelType: item.channelType || '',
        impower: item.impower || '',
        isRecommend: item.isRecommend || '',
        _id: item._id || '',
        status: String(updatedStatus),
      };

      await completeChannel(payload);

      toast.success(updatedStatus === 0 ? '已隐藏频道' : '已显示频道');
      queryClient.invalidateQueries({ queryKey: ['fetchChannelList'] });

    } catch (error) {
      toast.error('操作失败');
    }
  };

  const handleClick = (item: channelDetailList) => {
    
    sessionStorage.setItem('channelItem', JSON.stringify(item));
    window.location.href = `/channelManageDetail?id=${item._id}`;
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 pr-12">
      {data?.map((item) => (
        <div key={item._id} className={`
          relative w-[380px] h-[140px] border rounded-lg flex items-start pl-2 pr-2 pt-6 pb-4 group
          ${item.status === 0 ? 'opacity-50' : ''}
        `}

        >
          <div className='text-center'>
            <DropdownMenu >
              <DropdownMenuTrigger asChild>
                <div className="absolute top-1.5 right-2 mr-3 mt-1 z-10 cursor-pointer scale-[1.4] text-gray-400">
                  ···
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="bottom" className="w-[132px] px-2  py-3 ">
                <DropdownMenuItem className="border h-[33px]  rounded-sm flex items-center justify-center mb-2 cursor-pointer"
                  onSelect={() => handleOpenAdminDialog(item._id!)}
                >
                  设置频道运营
                </DropdownMenuItem>
                <DropdownMenuItem className="border h-[33px]  rounded-sm flex items-center justify-center mb-2 cursor-pointer z-50"
                  onSelect={() => handleToggleStatus(item)}
                >
                  {item.status === 1 ? '隐藏频道' : '显示频道'}
                </DropdownMenuItem>
                <DropdownMenuItem className="border h-[33px]  rounded-sm flex items-center justify-center cursor-pointer"
                  onSelect={() => handleDelete(item._id!)}
                >
                  删除频道
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* contents不参与布局计算 */}
          <div onClick={() => handleClick(item!)} className='contents cursor-pointer'>
            <div className="w-[95px] h-[95px] flex-shrink-0 ml-4 mt-0" >
              <img
                src={item.coverUrl || "	https://bhb-frontend.bhbcdn.com/static/files/8165e8edd4f649d1b50c3a7a825b7ce1.png"}
                className="w-full h-full object-cover rounded-lg bg-neutral-100"
              />
            </div>

            <div className="ml-4 mt-2 flex flex-col justify-start">
              <div className="mb-[18px] text-base font-normal">{item.channelName}</div>
              <div className="text-neutral-400 text-sm space-y-1">
                <p>频道成员：{item.peopleNum}</p>
                <p>频道号：{item.channelCode}</p>
              </div>
            </div>
          </div>
        </div>
      ))}

      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader className='flex  w-full items-center'>
            <div>当前频道运营</div>
          </DialogHeader>
          <AdminManagement
            channelId={selectedChannelId}
            showAddButton
          />
        </DialogContent>
      </Dialog>
    </div>


  );
}

// components/AdminManagement.tsx
interface AdminManagementProps {
  channelId: string;
  showAddButton?: boolean;
}

export const AdminManagement = ({
  channelId,
  showAddButton = true,

}: AdminManagementProps) => {
  const [open, setOpen] = useState(false);
  const [adminForm, setAdminForm] = useState<Omit<CreateAdminUser, 'szrChannelId'>>({
    username: '',
    name: '',
    password: '',
    role: 6,
    status: 0,
    qrCode: '',
    phone: ''
  });

  const [searchParams, setSearchParams] = useState({
    page: 1,
    size: 10,
    szrChannelId: channelId
  });

  const { data: adminData } = useQuery({
    queryKey: ['fetchAdminUser', searchParams],
    queryFn: () => fetchAdminUser(searchParams),
    enabled: !!channelId,
  });

  const queryClient = useQueryClient();
  const createMutation = useMutation({
    mutationFn: createAdminUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchAdminUser'] });
      setAdminForm({
        username: '',
        name: '',
        password: '',
        role: 6,
        status: 0,
        qrCode: '',
        phone: ''
      });
      setOpen(false);
      toast.success('管理员创建成功');
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    }
  });

  const deleteMutation = useMutation({
    mutationFn: removeAdminUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchAdminUser'] });
      toast.success('管理员删除成功');
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  const adminColumns: ColumnDef<AdminUserList>[] = [
    {
      header: '名称',
      accessorKey: 'name',
      cell: ({ row }) => <span>{row.original.name}</span>,
    },
    {
      header: '手机号',
      accessorKey: 'phone',
      cell: ({ row }) => <span>{row.original.phone}</span>,
    },
    {
      header: '角色',
      accessorKey: 'role',
      cell: ({ row }) => (
        <span>
          {row.original.role === 1 ? '管理员' :
            row.original.role === 2 ? '客服' :
              row.original.role === 4 ? '运营' :
                row.original.role === 5 ? '财务' : '频道运营'
          }
        </span>
      ),
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => (
        <span className={row.original.status === 0 ? 'text-green-500' : 'text-red-500'}>
          {row.original.status === 0 ? '已通过' : '未通过'}
        </span>
      ),
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          className="text-red-500 ml-[-15px]"
          onClick={() => deleteMutation.mutate({ id: row.original.id })}
        >
          删除
        </Button>
      ),
    },
  ];

  const handleSubmit = () => {
    if (!adminForm.username || !adminForm.name || !adminForm.password) {
      toast.warning('请填写完整信息');
      return;
    }

    createMutation.mutate({
      ...adminForm,
      szrChannelId: channelId,
    });
  };

  const handlePaginationChange = (newPagination: { pageIndex: number; pageSize: number }) => {
    setSearchParams(prev => ({
      ...prev,
      page: newPagination.pageIndex + 1,
      size: newPagination.pageSize
    }));
  };

  return (
    <div className="space-y-4">
      <div>
        {showAddButton && (
          <div className="flex justify-end">
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger asChild>
                <Button>新增</Button>
              </DialogTrigger>
              <DialogContent className="!max-w-none w-[67%] p-6">
                <DialogHeader className="pb-4 mb-4 w-full items-center border-b justify-center text-center">
                  <div>添加账号</div>
                </DialogHeader>

                <div className="flex flex-col items-center justify-center gap-8 m-4 ml-6 mt-4 pb-4">
                  <div className="flex text-left mr-4 pr-4 h-fit-content mt-2 mx-0">
                    <img src={RequiredIco} className="w-[14px] h-[14px]" />
                    <Label className="text-[14px] w-16">名称</Label>
                    <div className="w-[340px] ml-4 mt-[-6px]">
                      <Input
                        value={adminForm.name}
                        onChange={(e) => setAdminForm({ ...adminForm, name: e.target.value })}
                        placeholder="请输入用户姓名"
                      />
                    </div>
                  </div>
                  <div className="flex text-left mr-4 pr-4 h-fit-content mt-2">
                    <img src={RequiredIco} className="w-[14px] h-[14px]" />
                    <Label className="text-[14px] w-16">
                      手机号
                    </Label>
                    <div className="w-[340px] ml-4 mt-[-6px]">
                      <Input
                        className="w-full"
                        placeholder="请输入手机号"
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <img src={RequiredIco} className="w-[14px] h-[14px] mr-2" />
                    <Label className="w-20">用户名</Label>
                    <Input
                      value={adminForm.username}
                      onChange={(e) => setAdminForm({ ...adminForm, username: e.target.value })}
                      placeholder="请输入登录用户名"
                    />
                  </div>
                  <div className="flex items-center">
                    <img src={RequiredIco} className="w-[14px] h-[14px] mr-2" />
                    <Label className="w-20">密码</Label>
                    <Input
                      type="password"
                      value={adminForm.password}
                      onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}
                      placeholder="请输入密码"
                    />
                  </div>



                </div>

                <DialogFooter className="border-t pt-4">
                  <Button className="border border-gray-300" variant="ghost" onClick={() => setOpen(false)}>
                    取消
                  </Button>
                  <LoadingButton onClick={handleSubmit}>继续</LoadingButton>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}
        <DataTable
          columns={adminColumns as ColumnDef<unknown, unknown>[]}
          data={adminData?.data || []}
          rowCount={adminData?.totalSize}
          pagination={{
            pageIndex: (adminData?.page ?? 1) - 1,
            pageSize: adminData?.size ?? 10
          }}
          setPagination={() => {
           handlePaginationChange
          }}
          
        />
      </div>

    </div>
  );
};