import { useNavigate } from '@tanstack/react-router';


export function Crumbs() {
const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');

  const navigate = useNavigate();
  const handleButtonClick = () => {
    navigate({ to: '/channelManage' });
  };

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex justify-between  mt-2 ">
        <div className='cursor-pointer' onClick={handleButtonClick}>频道管理</div>
        <p className='ml-3 mr-3'>/</p> 
        <div className='cursor-pointer'>{item.channelName}_(频道号:{item.channelCode})</div>
      </div>
    </div>
  );
}
