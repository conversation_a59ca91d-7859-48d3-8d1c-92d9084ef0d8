import { useMemo, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { SearchCom } from "../searchCom";
import { Button } from '@mono/ui/button';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from "@/components/dataTable";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import { LoadingButton } from "@/components/loading-button";
import { Input } from "@mono/ui/input";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { createContentGategory, delContentGategory, fetchContentGategory, updateContentGategory, } from "@/api/channelManage";
import { toast } from "sonner";
import { CategoryItem, FetchCategoryParams } from "@/types/channelManage";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@mono/ui/dropdown-menu";
import { LoadingContainer } from '../../loading';



export function ClassificationManage() {
  const [searchParams, setSearchParams] = useState<Omit<FetchCategoryParams, 'channelId'>>({
    searchType: 1,
    searchText: '',
    startDate: undefined,
    endDate: undefined,
    page: 1,
    pageSize: 10,
  });
  console.log(searchParams);
  const [addUserDialogOpen, setAddClassificaDialogOpen] = useState(false);
  const [sort, setSort] = useState(false);
  const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');//当前转过来的频道号
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const [newCategory, setNewCategory] = useState({
    type: 1 as 1 | 2,
    name: '',
    status: 2 as 2 | 1,
    sort: 0
  });

  const { data: categoryData, isLoading } = useQuery({
    queryKey: ['fetchContentGategory', item._id, pagination, searchParams],
    queryFn: () => {
      const finalParams: FetchCategoryParams = {
        ...searchParams,
        channelId: item._id,
        page: pagination.pageIndex + 1,
        pageSize: pagination.pageSize,
        searchType: searchParams.searchType ?? 1, 
        startDate: searchParams.startDate ?? Date.now() - 365 * 24 * 60 * 60 * 1000,
        endDate: searchParams.endDate ?? Date.now(),
      };
      return fetchContentGategory(finalParams);
    },
    enabled: !!item?._id
  });



  const handleSearch = (newSearchParams: Partial<FetchCategoryParams>) => {
    setSearchParams(prev => ({
      ...prev,
      ...newSearchParams,
      searchType: newSearchParams.searchType ?? prev.searchType ?? 1, 
      page: 1, 
    }));
  };

  const createMutation = useMutation({
    mutationFn: createContentGategory,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchContentGategory'] });
      toast.success('分类创建成功');
    },
    onError: () => {
      toast.error('错误');
    }
  });

  const handleDelete = async (_id?: string) => {
    if (!_id) return;
    try {
      await delContentGategory({ _id });
      queryClient.invalidateQueries({ queryKey: ['fetchContentGategory'] });
      toast.success('分类已删除');
    } catch (error) {
      toast.error('错误');
    }
  };

  const [draggedItem, setDraggedItem] = useState<CategoryItem | null>(null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [sortedItems, setSortedItems] = useState<CategoryItem[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const handleDragStart = (item: CategoryItem) => {
    setDraggedItem(item);
    setIsDragging(true);
  };
  const handleDragOver = (e: React.DragEvent, itemId: string) => {
    e.preventDefault();
    if (draggedItem?._id !== itemId) {
      setHoveredItem(itemId);
    }
  };
  const handleDragEnd = () => {
    setHoveredItem(null);
    setIsDragging(false);
  };

  const handleDrop = (targetItemId: string) => {
    if (!draggedItem || draggedItem._id === targetItemId) return;

    const currentItems = sortedItems.length > 0 ? [...sortedItems] : [...(categoryData || [])];
    const fromIndex = currentItems.findIndex(item => item._id === draggedItem._id);
    const toIndex = currentItems.findIndex(item => item._id === targetItemId);

    if (fromIndex === -1 || toIndex === -1) return;

    const newItems = [...currentItems];
    const [removed] = newItems.splice(fromIndex, 1);
    newItems.splice(toIndex, 0, removed);
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      sort: 1000 - index * 1
    }));

    setSortedItems(updatedItems);
    setHoveredItem(null);
  };

  const handleSaveSort = async () => {
    if (sortedItems.length === 0) {
      toast.info('没有排序变更需要保存');
      return;
    }

    try {
      await Promise.all(
        sortedItems.map(item =>
          updateContentGategory({
            ...item,
            channelId: item.channelId,
            name: item.name,
            type: item.type,
            status: item.status ?? 2,
          })
        )
      );

      queryClient.invalidateQueries({ queryKey: ['fetchContentGategory'] });
      toast.success('排序已保存');
      setSort(false);
    } catch (error) {
      toast.error('保存排序失败');
    }
  };

  const handleCancelSort = () => {
    setSort(false);
    setSortedItems([]);
    setHoveredItem(null);
    setIsDragging(false);
  };

  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [editingCategory, setEditingCategory] = useState<CategoryItem | null>(null);
  const handleConfirmStatusChange = async (id: string) => {
    const category = categoryData?.find(item => item._id === id);

    if (!category) return;
    try {
      await updateContentGategory({
        ...category,
        status: category.status === 1 ? 2 : 1
      });
      queryClient.invalidateQueries({ queryKey: ['fetchContentGategory'] });
      toast.success(`已${category.status === 1 ? '下架' : '上架'}`);

    } catch (error) {
      toast.error('操作失败');
    }
  };


  const sortedData = useMemo(() => {
    return [...(categoryData ?? [])].sort((a, b) => {
      const aSort = typeof a.sort === 'number' ? a.sort : 0;
      const bSort = typeof b.sort === 'number' ? b.sort : 0;
      return bSort - aSort; 
    });
  }, [categoryData]);

  const handleEdit = (category: CategoryItem) => {
    setDialogMode('edit');
    setEditingCategory(category);
    setNewCategory({
      type: category.type,
      name: category.name,
      status: category.status ?? 2, 
      sort: category.sort || 0
    });
    setAddClassificaDialogOpen(true);
  };

  const handleSubmitCategory = async () => {
    if (!newCategory.name.trim()) {
      toast.warning('分类名称不能为空');
      return;
    }

    try {
      if (dialogMode === 'create') {
        await createMutation.mutateAsync({
          channelId: item._id,
          ...newCategory
        });
      } else if (editingCategory) {
        await updateContentGategory({
          _id: editingCategory._id,
          channelId: editingCategory.channelId,
          ...newCategory
        });
        toast.success('修改成功!')
      }
      setAddClassificaDialogOpen(false);
      queryClient.invalidateQueries({ queryKey: ['fetchContentGategory'] });
    } catch (error) {
      toast.error('操作失败');
    }
  };

  const displayData = useMemo(() => {
    if (!sortedItems || sortedItems.length === 0) return [];
    return [...sortedItems].sort((a, b) => (a.sort ?? Number.MIN_SAFE_INTEGER) - (b.sort ?? Number.MIN_SAFE_INTEGER));
  }, [sortedItems]);
  // console.log('排序前拿到的数据:', sortedItems);
  // console.log('排序后排好的数据:', displayData);

  const classColumns: ColumnDef<CategoryItem>[] = [
    {
      header: '分类类型',
      accessorKey: 'type',
      cell: ({ row }) => (
        <div
          className="flex items-center"
          draggable={sort}
          onDragStart={() => sort && handleDragStart(row.original)}
          onDragOver={(e) => sort && handleDragOver(e, row.original._id)}
          onDragEnd={() => sort && handleDragEnd()}
          onDrop={() => sort && handleDrop(row.original._id)}
        >
          {sort && (
            <img
              className="w-[16px] h-[16px] mr-8 mt-1 cursor-move"
              src="https://bhb-frontend.bhbcdn.com/static/files/17ef4c8843954385be8c84567e1552d7.png"
              draggable
            />
          )}
          <span
            className={`
            ${hoveredItem === row.original._id ? 'border-b-2 border-blue-500' : ''}
            ${draggedItem?._id === row.original._id && isDragging ? 'opacity-50' : ''}
          `}
          >
            {row.original.type === 1 ? '文案' : '素材'}
          </span>
        </div>
      ),
    },
    {
      header: '分类名称',
      accessorKey: 'name',
      cell: ({ row }) => <span>{row.original.name}</span>,
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => (
        <span className="gap-1 flex">
          <span className={row.original.status === 1 ? 'text-green-500 scale-150' : 'text-red-500  scale-150'}>•</span>
          {row.original.status === 1 ? '上架' : '下架'}
        </span>
      ),
    },
    {
      header: '添加时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => (
        <span>{new Date(row.original.createdAt || '').toLocaleString()}</span>
      )
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2.5 text-blue-500">
          {/* 编辑名字 */}
          <Button
            variant="ghost"
            className="p-0"
            onClick={() => handleEdit(row.original)}
          >
            编辑
          </Button>


          {/* 上架/下架 */}
          <DropdownMenu >
            <DropdownMenuTrigger asChild className="border-none">
              <Button
                variant="ghost"
                className="p-0"
              >
                {row.original.status === 1 ? '下架' : '上架'}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="bottom" className="px-1  py-1 p-4 ">
              <div className="mb-4 flex">
                <svg className="icon m-2 mt-0.5" viewBox="0 0 1024 1024" version="1.1" width="19" height="19">
                  <path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64z m32 704h-64v-64h64v64z m-64-128V256h64v384h-64z" fill="#ff7d00"></path>
                </svg>
                确定要{row.original.status === 1 ? '下架' : '上架'}该分类吗?
              </div>
              <div className="flex gap-2 justify-end">
                <DropdownMenuItem
                  className=" h-[36px]  text-xs border p-4 border-gray-200 rounded-md flex items-center justify-center cursor-pointer"
                >
                  取消
                </DropdownMenuItem>

                <Button className="..."
                  onClick={() => {
                    handleConfirmStatusChange(row.original._id);
                  }}>确定
                </Button>
              </div>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 删除分类 */}
          <DropdownMenu >
            <DropdownMenuTrigger asChild className="border-none">
              <Button
                variant="ghost"
                className="p-0 border-none"
              >
                更多
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent side="bottom" className="px-1  py-1 w-20">
              <DropdownMenuItem className=" h-[33px]  rounded-sm flex items-center justify-center cursor-pointer"
                onClick={() => handleDelete(row.original._id)}
              >
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  ];


  return (
    <div className=" items-center justify-between mb-4">
      <div className="flex items-center mb-2 gap-3 mt-8">
        <Dialog open={addUserDialogOpen} onOpenChange={setAddClassificaDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" onClick={() => {
              setDialogMode('create');
              setNewCategory({
                type: 1,
                name: '',
                status: 2,
                sort: 0
              });
              setAddClassificaDialogOpen(true);
            }}>
              新建分类
            </Button>
          </DialogTrigger>
          <DialogContent className="!max-w-none w-[600px]  p-6 ">
            <DialogHeader className=" pb-4  w-full items-center justify-center text-center border-b border-gray-200">
              <div>{dialogMode === 'create' ? '新增分类' : '编辑分类'}</div>
            </DialogHeader>

            <div className=" gap-2 ml-2 mb-4">
              <div className="flex gap-2">

                <Button
                  className="rounded-2xl border border-gray-200"
                  variant={newCategory.type === 1 ? 'default' : 'ghost'}
                  onClick={() => setNewCategory({ ...newCategory, type: 1 })}
                  disabled={dialogMode === 'edit'}
                >
                  文案
                </Button>
                <Button
                  className="rounded-2xl border border-gray-200"
                  variant={newCategory.type === 2 ? 'default' : 'ghost'}
                  onClick={() => setNewCategory({ ...newCategory, type: 2 })}
                  disabled={dialogMode === 'edit'}
                >
                  素材
                </Button>
              </div>
              <div className="h-[65px] flex mt-3 gap-2">
                <div className="flex mt-4">
                  <img src={RequiredIco} className="w-[14px] h-[14px]" />
                  <div className="h-1/2 w-full">分类名称</div>
                </div>
                <div className="h-1/2 w-[70%] ml-1 mt-3">
                  <Input
                    placeholder="请输入分类名称"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                  />
                </div>
              </div>
            </div>

            <DialogFooter className=''>
              <Button variant="ghost" className='border border-gray-300' onClick={() => setAddClassificaDialogOpen(false)}>
                取消
              </Button>
              <LoadingButton
                // loading={createMutation.isLoading}
                onClick={handleSubmitCategory}
              >
                {dialogMode === 'create' ? '创建' : '保存'}
              </LoadingButton>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        {!sort ? (
          <Button variant="outline" onClick={() => setSort(true)}>排序</Button>
        ) : (
          <>
            <Button
              variant="outline"
              onClick={handleSaveSort}
              disabled={sortedItems.length === 0}
            >
              保存排序
            </Button>
            <Button variant="outline" onClick={handleCancelSort}>取消排序</Button>
          </>
        )}

      </div>

      <SearchCom
        onSearch={(params) => {
          const transformedParams = {
            ...params,
            searchType: params.searchType ?? 1,
            startDate: params.startDate ? new Date(params.startDate).getTime() : undefined,
            endDate: params.endDate ? new Date(params.endDate).getTime() : undefined,
          };

          handleSearch(transformedParams);
        }}
        searchTypeOptions={[
          { value: 1, label: '分类名称' }
        ]}
      />

      {isLoading ? (<div className="fixed inset-0 flex items-center justify-center ml-[12%] mt-[24px] z-50">
        <LoadingContainer />
      </div>) : (
        <DataTable
          columns={classColumns}
          data={sort && sortedItems.length > 0 ? sortedItems : sortedData || []}
          rowCount={displayData?.length || 0}
          pagination={pagination}
          setPagination={setPagination}
        />


      )}

    </div>
  );
}
