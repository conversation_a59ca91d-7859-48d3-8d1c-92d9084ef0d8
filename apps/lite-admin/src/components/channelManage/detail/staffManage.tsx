import { useState } from "react";
import { SearchCom } from "../searchCom";
import { AdminUserList, ChannelMemberItem, CreateAdminUser, FetchChannelMembersResponse, FetchChannelParams, } from '@/types/channelManage';
import { Button } from '@mono/ui/button';
import { DataTable } from "@/components/dataTable";
import { formatDate } from '@mono/utils/day';
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { addMembers, createAdminUser, fetchAdminUser, fetchMembers, removeAdminUser, removeMembers } from "@/api/channelManage";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import { Input } from "@mono/ui/input";
import { LoadingButton } from "@/components/loading-button";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Label } from '@mono/ui/label';
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mono/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@mono/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";



export function StaffManage() {
  const getChannelMembers = (
  ): Array<ColumnDef<ChannelMemberItem>> => [
      {
        header: '用户昵称',
        accessorKey: '_id',
        cell: ({ row }) => <span>{row.original.nickName || ''}</span>,
      },
      {
        header: '用户编号',
        accessorKey: 'phone',
        cell: ({ row }) => <span>{row.original._id || ''}</span>,
      },
      {
        header: '手机号',
        accessorKey: 'nickName',
        cell: ({ row }) => <span>{row.original.phone || ''}</span>,
      },
      {
        header: '加入时间',
        accessorKey: 'createdAt',
        cell: ({ row }) =>
          <span>{formatDate(new Date(row.original.createdAt!).getTime()) || '--'}</span>
      },
      {
        header: '操作',
        accessorKey: 'delete',
        cell: ({ row }) =>
          <div>
            <DropdownMenu >
              <DropdownMenuTrigger asChild className="border-none">
                <Button
                  variant="ghost"
                  className="p-0 border-none text-blue-500 cursor-pointer"
                >
                  移出频道
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="bottom" className="p-4  flex-col w-40 h-24 ">
                <Label> 是否确认移出该频道？</Label>
                <div className="flex justify-end gap-1 mt-2">
                  <DropdownMenuItem className="text-xs h-[33px] border rounded-sm flex items-center justify-center cursor-pointer"
                  >
                    取消
                  </DropdownMenuItem>
                  <DropdownMenuItem className="text-xs h-[33px] bg-green-500 border rounded-sm flex items-center justify-center cursor-pointer"
                    onClick={() => handleDelete(row.original.id)}
                  >
                    确认
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
      },
    ];


  const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');
  console.log(item._id);
  const [typeClass, setTypeClass] = useState<number>(1);
  const [addType, setAddType] = useState<'phone' | 'userId'>('phone');
  const [inputValue, setInputValue] = useState('');
  const [open, setOpen] = useState(false);
  const [adminForm, setAdminForm] = useState<Omit<CreateAdminUser, 'szrChannelId'>>({
    username: '',
    name: '',
    password: '',
    role: 6,//默认值为6频道运营
    status: 0,
    qrCode: '',
    phone: ''
  });

  const [searchParams, setSearchParams] = useState<FetchChannelParams>({
    searchType: 1,
    searchText: undefined,
    startDate: undefined,
    endDate: undefined
  });
  const handleSearch = (newSearchParams: Partial<FetchChannelParams>) => {
    setSearchParams(prev => ({
      ...prev,
      ...newSearchParams,
      searchType: newSearchParams.searchType ?? prev.searchType ?? 1,
      page: 1,
    }));
  };


  const pag = {
    pageIndex: 1,
    pageSize: 10
  }

  const { data: membersData } = useQuery<FetchChannelMembersResponse>({
    queryKey: ['fetchMembers', 'approved', item?._id, pag, searchParams],
    queryFn: () => {
      const queryParams = {
        ...searchParams,
        page: pag.pageIndex,
        size: pag.pageSize,
        status: 1 as unknown as "0" | "1" | undefined,//申请通过人员
        channelId: item?._id || '',
        searchType: searchParams.searchType ?? 1,
        startDate: typeof searchParams.startDate === 'string'
          ? new Date(searchParams.startDate).getTime()
          : searchParams.startDate == null
            ? undefined
            : searchParams.startDate,
        endDate: typeof searchParams.endDate === 'string'
          ? new Date(searchParams.endDate).getTime()
          : searchParams.endDate == null
            ? undefined
            : searchParams.endDate,
      };
      return fetchMembers(queryParams);
    },
    enabled: !!item?._id && typeClass === 1,
  });

  // 申请
  const { data: applyData } = useQuery<FetchChannelMembersResponse>({
    queryKey: ['fetchMembers', 'pending', item?._id, pag, searchParams],
    queryFn: () => {
      const queryParams = {
        ...searchParams,
        page: pag.pageIndex,
        size: pag.pageSize,
        status: 0 as unknown as "0" | "1" | undefined,
        channelId: item?._id || '',
        searchType: searchParams.searchType ?? 1,
        startDate: typeof searchParams.startDate === 'string'
          ? new Date(searchParams.startDate).getTime()
          : searchParams.startDate == null
            ? undefined
            : searchParams.startDate,
        endDate: typeof searchParams.endDate === 'string'
          ? new Date(searchParams.endDate).getTime()
          : searchParams.endDate == null
            ? undefined
            : searchParams.endDate,
      };
      return fetchMembers(queryParams);
    },
    enabled: !!item?._id && typeClass === 1,
  });


  const { data: adminData } = useQuery({
    queryKey: ['fetchAdminUser', searchParams],
    queryFn: () => fetchAdminUser({
      ...searchParams,
      szrChannelId: item._id,
    }),
    enabled: !!item?._id && typeClass === 2,
  });


  const queryClient = useQueryClient();
  const saveMutation = useMutation({
    mutationFn: addMembers,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchMembers', 'approved'] });
      toast.success('新建成功');
      setInputValue('')
      setOpen(false)

    },
    onError: (error) => {
      toast.error(`新建失败: ${error.message}`);
    }
  })

  const createMutation = useMutation({
    mutationFn: createAdminUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchAdminUser'] });
      setAdminForm({
        username: '',
        name: '',
        password: '',
        role: 6,//固定为6
        status: 0,
        qrCode: '',
        phone: ''
      });
      setOpen(false);
      toast.success('管理员创建成功');
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`);
    }
  });

  const handleDelete = async (id?: string) => {
    if (!id) return;
    try {
      await removeMembers({ id });
      toast.success('移除成功');
      queryClient.invalidateQueries({ queryKey: ['fetchMembers', 'approved'] });

    } catch (error) {
      toast.error('错误');
    }
  };

  const deleteMutation = useMutation({
    mutationFn: removeAdminUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchAdminUser'] });
      toast.success('管理员删除成功');
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  const adminColumns: ColumnDef<AdminUserList>[] = [
    {
      header: '名称',
      accessorKey: 'name',
      cell: ({ row }) => <span>{row.original.name}</span>,
    },
    {
      header: '用户名',
      accessorKey: 'username',
      cell: ({ row }) => <span>{row.original.username}</span>,
    },
    {
      header: '角色',
      accessorKey: 'role',
      cell: ({ row }) => (
        <span>
          {row.original.role === 1 ? '管理员' :
            row.original.role === 2 ? '客服' :
              row.original.role === 4 ? '运营' :
                row.original.role === 5 ? '财务' : '频道运营'
          }
        </span>
      ),
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => (
        <span className={row.original.status === 0 ? 'text-green-500' : 'text-red-500'}>
          {row.original.status === 0 ? '已通过' : '未通过'}
        </span>
      ),
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          className="text-red-500 ml-[-16px]"
          onClick={() => deleteMutation.mutate({ id: row.original.id })}
        >
          删除
        </Button>
      ),
    },
  ];

  // 审批用户弹窗 status字段不存在
  const applyUser: ColumnDef<ChannelMemberItem>[] = [
    {
      header: '用户编号',
      accessorKey: 'name',
      cell: ({ row }) => <span>{row.original.id}</span>,
    },
    {
      header: '用户昵称',
      accessorKey: 'username',
      cell: ({ row }) => <span>{row.original.nickName}</span>,
    },
    {
      header: '申请时间',
      accessorKey: 'role',
      cell: ({ row }) => (
        <span>
          {row.original.updatedAt}
        </span>
      ),
    },
    {
      header: '申请状态',
      accessorKey: 'status',
      cell: ({ row }) => (
        <div>
          {row.original.status === 0 && (
            <span className="text-red-500">等待审批用户</span>
          )}
        </div>
      ),
    },
    {
      header: '操作',
      cell: () => (
        <div>
          <Button
            variant="ghost"
            className="text-blue-500 ml-[-16px]"
          >
            同意申请
          </Button>
          <Button
            variant="ghost"
            className="text-red-500 ml-[-16px]"
          >
            不同意申请
          </Button>
        </div>
      ),
    },
  ];

  const handleSubmit = () => {
    if (!adminForm.username || !adminForm.name || !adminForm.password) {
      toast.warning('请填写完整信息');
      return;
    }

    createMutation.mutate({
      ...adminForm,
      szrChannelId: item._id,
    });
  };

  return (
    <div>
      <div className="flex items-center justify-between w-full mb-6">
        <div className="flex items-center gap-4">
          <button
            onClick={() => setTypeClass(1)}
            className={`px-4 py-2 rounded-3xl transition-colors ${typeClass === 1
              ? ' text-green-600 border border-green-600'
              : 'bg-gray-100 text-gray-700 border border-gray-200'
              }`}
          >
            频道成员
          </button>

          <button
            onClick={() => setTypeClass(2)}
            className={`px-4 py-2 rounded-3xl transition-colors ${typeClass === 2
              ? ' text-green-600 border border-green-600'
              : 'bg-gray-100 text-gray-700 border border-gray-200'
              }`}
          >
            频道运营
          </button>
        </div>


        <div className="flex items-center gap-4">
          {typeClass === 1 && (<>
            <Dialog >
              <DialogTrigger asChild>
                <Button>申请列表</Button>
              </DialogTrigger>


              <DialogContent className="!max-w-none w-[80vw]">
                <DialogHeader>频道用户申请列表</DialogHeader>

                <div>
                  <DataTable
                    columns={applyUser}
                    data={(applyData?.data || [])}
                    // data={(membersData?.data || []).filter(item => item.status === 0)}
                    rowCount={applyData?.totalSize}
                    pagination={{
                      pageIndex: (applyData?.page ?? 1) - 1,
                      pageSize: applyData?.size ?? 10
                    }}
                    setPagination={(newPag) => {
                      console.log('分页:', newPag);
                    }}
                  />
                </div>
                <DialogFooter>

                </DialogFooter>
              </DialogContent>
            </Dialog>
          </>)}

          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <Button >
                新增
              </Button>
            </DialogTrigger>
            {typeClass === 1 ? (
              <DialogContent className="!max-w-none w-[600px]  p-6">
                <DialogHeader className=" pb-4 mb-4 w-full items-center border-b justify-center text-center">
                  <div>新增人员</div>
                </DialogHeader>

                <div className="flex flex-col gap-2 m-4 ml-6 mt-4 pb-4 ">
                  <div className="flex text-left gap-6 justify-start mr-4 pr-4 h-fit-content">
                    <div className="flex">
                      <img src={RequiredIco} className="w-[14px] h-[14px]" />
                      <Label className="text-[14px] w-[70px]">
                        <Select value={addType} onValueChange={(value: 'phone' | 'userId') => setAddType(value)}>
                          <SelectTrigger className="w-30 mr-2 text-[14px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent >
                            <SelectItem value="phone">手机号</SelectItem>
                            <SelectItem value="userId">用户编号</SelectItem>
                          </SelectContent>
                        </Select>

                      </Label>
                    </div>
                    <div className="w-[280px] ml-4 ">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        className="w-full"
                        placeholder={addType === 'phone' ? '请输入手机号' : '请输入用户编号'}
                      />
                      <small className="flex text-gray-600 mt-3">
                        <svg className="icon w-3 h-3 mt-1 mr-0.5" viewBox="0 0 1024 1024" >
                          <path d="M511.5 1019.5c-280.1 0-508-227.9-508-508s227.9-508 508-508 508 227.9 508 508-227.9 508-508 508z m0-967c-253.1 0-459 205.9-459 459s205.9 459 459 459 459-205.9 459-459-205.9-459-459-459zM536 645.7V217.1c0-13.5-11-24.5-24.5-24.5s-24.5 11-24.5 24.5v428.7c0 13.5 11 24.5 24.5 24.5s24.5-11 24.5-24.6z m-24.5 86.8c-20.3 0-36.7 16.4-36.7 36.7s16.4 36.7 36.7 36.7c20.3 0 36.7-16.4 36.7-36.7s-16.4-36.7-36.7-36.7z" fill="#707070" >
                          </path>
                        </svg>
                        加入频道用户必须为小程序用户后才可录入
                      </small>
                    </div>
                  </div>
                </div>

                <div className='w-full'>
                  <DialogFooter className="border-t pt-4">
                    <Button className="border border-gray-300" variant="ghost" onClick={() => setOpen(false)}>
                      取消
                    </Button>
                    <LoadingButton
                      onClick={() => {
                        if (!inputValue) {
                          toast.error('请输入内容');
                          return;
                        }

                        const payload = {
                          channelId: item?._id,
                          [addType]: inputValue,
                        };
                        saveMutation.mutate(payload);
                      }}
                    >
                      继续
                    </LoadingButton>

                  </DialogFooter>
                </div>
              </DialogContent>
            ) : (

              <DialogContent className="!max-w-none w-[67%]  p-6">
                <DialogHeader className=" pb-4 mb-4 w-full items-center border-b justify-center text-center">
                  <div>添加账号</div>
                </DialogHeader>

                <div className="flex flex-col items-center justify-center  gap-8 m-4 ml-6 mt-4 pb-4">
                  <div className="flex text-left  mr-4 pr-4 h-fit-content mt-2 mx-0">
                    <img src={RequiredIco} className="w-[14px] h-[14px]" />
                    <Label className="text-[14px] w-16">
                      名称
                    </Label>
                    <div className="w-[340px] ml-4 mt-[-6px]">
                      <Input
                        value={adminForm.name}
                        onChange={(e) => setAdminForm({ ...adminForm, name: e.target.value })}
                        placeholder="请输入用户姓名"
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <img src={RequiredIco} className="w-[14px] h-[14px] mr-2" />
                    <Label className="w-20">用户名</Label>
                    <Input
                      value={adminForm.username}
                      onChange={(e) => setAdminForm({ ...adminForm, username: e.target.value })}
                      placeholder="请输入登录用户名"
                    />
                  </div>
                  <div className="flex items-center">
                    <img src={RequiredIco} className="w-[14px] h-[14px] mr-2" />
                    <Label className="w-20">密码</Label>
                    <Input
                      type="password"
                      value={adminForm.password}
                      onChange={(e) => setAdminForm({ ...adminForm, password: e.target.value })}
                      placeholder="请输入密码"
                    />
                  </div>

                </div>

                <div className='w-full'>
                  <DialogFooter className="border-t pt-4">
                    <Button className="border border-gray-300" variant="ghost" onClick={() => setOpen(false)}>
                      取消
                    </Button>
                    <LoadingButton onClick={handleSubmit}>继续</LoadingButton>
                  </DialogFooter>
                </div>
              </DialogContent>

            )}
          </Dialog>

        </div>
      </div>
      <div className="flex items-center justify-between mb-4">
        <SearchCom onSearch={(params) => {
          const transformedParams = {
            ...params,
            searchType: params.searchType ?? 1,
            startDate: params.startDate ? String(new Date(params.startDate).getTime()) : undefined,
            endDate: params.endDate ? String(new Date(params.endDate).getTime()) : undefined,
          };

          handleSearch(transformedParams);
        }}
          searchTypeOptions={[
            { value: 1, label: '分类名称' }
          ]} />
        <div className="gap-2 flex mt-4">

          {typeClass === 1 && <>
            <Button variant="outline">导出数据</Button>
            <Button variant="outline">导出记录</Button>
          </>}

        </div>
      </div>

      <div className="mt-6">
        {typeClass === 1 ? (
          <div>
            <DataTable
              columns={getChannelMembers()}
              data={membersData?.data || []}
              rowCount={membersData?.totalSize}
              pagination={{
                pageIndex: (membersData?.page ?? 1) - 1,
                pageSize: membersData?.size ?? 10
              }}
              setPagination={(newPag) => {
                console.log('新的分页:', newPag);
              }}
            />

          </div>
        ) : (
          <div>
            <div>
              <DataTable
                columns={adminColumns}
                data={adminData?.data || []}
                rowCount={adminData?.totalSize}
                pagination={{
                  pageIndex: (adminData?.page ?? 1) - 1,
                  pageSize: adminData?.size ?? 10
                }}
                setPagination={(newPag) => {
                  console.log('新的分页:', newPag);
                }}
              />
            </div>
          </div>
        )}
      </div>

    </div>
  );
}




