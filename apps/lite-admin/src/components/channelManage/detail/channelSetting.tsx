import { Button } from "@mono/ui/button";
import { Textarea } from "@mono/ui/textarea";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Label } from "@mono/ui/label";
import { toast } from 'sonner';
import { Input } from "@mono/ui/input";
import { useState } from "react";
import { saveChannelWithCover } from "@/api/channelManage";
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import { LoadingButton } from "@/components/loading-button";
import { RadioGroup, RadioGroupItem } from "@mono/ui/radio-group";
import { MediaUpload } from "@/components/customizedAICharacter/AdvanceUpload";



export function ChannelSetting() {
  const search = new URLSearchParams(location.search);
  const id = search.get('id');
  const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');
  const [isVideoDeleted, setIsVideoDeleted] = useState(false);

  const [formData, setFormData] = useState({
    channelName: item.channelName || '',
    description: item?.description || '',
    coverUrl: item?.coverUrl || 'https://bhb-frontend.bhbcdn.com/static/files/8165e8edd4f649d1b50c3a7a825b7ce1.png',
    tempCoverFile: null as File | null,

    channelType: item?.channelType || '1',
    impower: item?.impower || '1',
    isRecommend: String(item?.isRecommend) || 'false',
    status: item?.status?.toString() || '1'
  });

  const [uploadProgress, setUploadProgress] = useState(0);
  const [defaultContent, setDefaultContent] = useState(false);
  const handleSubmit = async () => {
    if (!formData.channelName.trim()) {
      toast.error('频道名称不能为空');
      return;
    }


    try {
      await saveChannelWithCover({
        _id: id!,
        channelName: formData.channelName,
        description: formData.description,
        coverFile: formData.tempCoverFile,
        currentCoverUrl: formData?.coverUrl,
        channelType: formData?.channelType || '1',
        impower: formData?.impower || '1',
        isRecommend: formData?.isRecommend,
        status: item?.status ? String(item.status) : '1'
      },);

      setDefaultContent(false)
      toast.success('频道更新成功');
      const cachedItem = JSON.parse(sessionStorage.getItem('channelItem') || '{}');
      sessionStorage.setItem('channelItem', JSON.stringify({
        ...cachedItem,
        channelName: formData.channelName,
        coverUrl: formData.coverUrl,
        description: formData.description,
        channelType: formData.channelType,
        impower: formData.impower,
        isRecommend: String(formData.isRecommend),
        status: formData.status
      }));

      setFormData(prev => ({
        ...prev,
        tempCoverFile: null 
      }));

    } catch (error) {
      toast.error(error instanceof Error ? error.message : '更新失败');
    } finally {
      setUploadProgress(0);
    }
  };

  const handleImageUrl = (file?: File) => {
    console.log(isVideoDeleted);
    console.log(file);
    if (!file) return ;
    if (!['image/jpeg', 'image/png'].includes(file.type)) {
      toast.warning('仅支持 JPG/PNG 格式图片');
      return;
    }
    if (file.size > 3 * 1024 * 1024) {
      toast.warning('图片大小不能超过 3MB');
      return;
    }

    setFormData(prev => ({
      ...prev,
      tempCoverFile: file,
      coverUrl: URL.createObjectURL(file) 
    }));
  };

  const customStyle = {
    cursor: 'pointer',
    borderRadius: '0.5rem',
    border: '1px dashed #6ee7b7',
    width: '20rem',
    maxHeight: '100%',
    maxWidth: '100%',
    objectFit: 'cover' as const,
  };

  return (
    <div className="h-screen w-full p-4 box-border">
      <div className="h-[3%] flex items-center justify-between">
        <div className="flex text-left justify-start h-fit-content mt-8">
          <img src={RequiredIco} className="w-[14px] h-[14px]" />
          <Label className="text-[14px] w-16">频道名称</Label>
          <div className="w-[340px] ml-4 mt-[-6px]">
            <Input
              value={formData.channelName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                channelName: e.target.value
              }))}
            />
          </div>

        </div>
        <div className=" px-4 overflow-auto ">
          <Dialog open={defaultContent} onOpenChange={setDefaultContent}>
            <DialogTrigger asChild>
              <Button size="sm" className="border border-gray-200 "  >
                频道加入要求
              </Button>
            </DialogTrigger>


            <DialogContent className="!max-w-none w-[60%]  p-6 pt-2 pb-0">
              <DialogHeader className="pt-7  pb-3 mb-0 w-full items-center mt-[-20px] border-b border-gray-200 justify-center text-center">
                <div>频道加入要求</div>
              </DialogHeader>

              {/* 中心功能 */}
              <div className=" w-full p-4 pb-0 flex pt-0 flex-col gap-4">
                {/* 类型功能 */}
                <div className=" h-[20%] flex flex-col   rounded-lg p-4 pb-0 pt-0 ">
                  <div className="h-[90%]  items-center">
                    <span className="text-sm font-medium">频道类型</span>
                  </div>
                  <RadioGroup
                    className="h-[40%] flex gap-4"
                    value={String(formData.channelType)}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, channelType: value }))}
                  >
                    {[
                      {
                        value: '1',
                        title: '私有频道',
                        desc: '需要申请后管理员同意才可加入',
                        id: 'self',
                      },
                      {
                        value: '2',
                        title: '公开频道',
                        desc: '无需任何审批，可直接加入',
                        id: 'open',
                      },
                    ].map((option) => (
                      <div key={option.value} className="w-1/2 h-full flex">
                        <RadioGroupItem value={option.value} id={option.id} />
                        <Label htmlFor={option.id} className="flex">
                          <div className="text-sm p-2 mt-[-10px]">{option.title}</div>
                          <span className="text-gray-400 text-xs p-2.5 pt-0">({option.desc})</span>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                {/* 授权功能 */}
                <div className="flex-3 flex flex-col rounded-lg p-4">
                  <div className="h-[5%] flex items-center mb-2">
                    <span className="text-sm font-medium">授权方式</span>
                  </div>

                  <div className="h-[98%] flex flex-col gap-4 mt-2 w-[90%]">
                    {[
                      {
                        value: '1',
                        title: '无需授权',
                        desc: '用户加入频道后无须授权,并且频道不提供授权按钮',
                        img: 'https://bhb-frontend.bhbcdn.com/static/files/images/aafd883fa3fe4519bc3b41491003db38.png',
                      },
                      {
                        value: '2',
                        title: '自主授权',
                        desc: '用户加入频道后，可选择是否进行授权数字人形象及声音给频道',
                        img: 'https://bhb-frontend.bhbcdn.com/static/files/images/86bfe2b9b4ae4c7bbecfa336ffdf183e.png',
                      },
                      {
                        value: '3',
                        title: '必须授权',
                        desc: '加入前，需授权数字人形象与声音供频道使用，否则无法加入及查看频道内容',
                        img: 'https://bhb-frontend.bhbcdn.com/static/files/images/1d707e58fb7c43679d420850f3db8266.png',
                      },
                    ].map((option) => (
                      <div
                        key={option.value}
                        className={`flex-1 flex gap-4 p-9 border rounded-xl cursor-pointer ${String(formData.impower) === option.value ? 'border-green-500 bg-green-50 dark:bg-black' : ''
                          }`}
                        onClick={() => setFormData((prev) => ({ ...prev, impower: option.value }))}
                      >
                        <div className="w-[10%] h-full flex items-center justify-center rounded">
                          <img className="w-14" src={option.img} alt="" />
                        </div>
                        <div className="w-[90%] h-full flex flex-col justify-center rounded">
                          <div>{option.title}</div>
                          <div>{option.desc}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 推荐功能 */}
                <div className="flex flex-col  rounded-lg p-4 ">
                  <div className="h-[20%] flex items-center">
                    <span className="text-sm font-medium">是否展示在推荐列表</span>
                  </div>

                  <RadioGroup
                    className="h-[80%] flex gap-4 p-4 pl-0 pb-0"
                    value={String(formData.isRecommend)}
                    onValueChange={(value) => setFormData((prev) => ({ ...prev, isRecommend: value }))}
                  >
                    {[
                      {
                        value: 'true',
                        title: '展示',
                        desc: '用户可在推荐列表看到该频道',
                        id: 'show',
                      },
                      {
                        value: 'false',
                        title: '不展示',
                        desc: '用户需要搜索频道号后，才可找到该频道',
                        id: 'unshow',
                      },
                    ].map((option) => (
                      <div key={option.value} className="w-1/2 h-full flex">
                        <RadioGroupItem value={option.value} id={option.id} />
                        <Label htmlFor={option.id} className="flex">
                          <div className="text-sm p-2 mt-[-10px]">{option.title}</div>
                          <span className="text-gray-400 text-xs p-2.5 pt-0">({option.desc})</span>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>

                </div>
              </div>

              <DialogFooter className="pb-5">
                <Button variant="ghost" className='border border-gray-300 w-20 ' onClick={() => setDefaultContent(false)}>
                  取消
                </Button>
                <LoadingButton className="w-20" onClick={handleSubmit}>
                  继续
                </LoadingButton>

              </DialogFooter>
            </DialogContent>

          </Dialog>
        </div>
      </div>


      <div className="h-[3%] flex items-center space-x-0 mt-12 border-l  border-green-400 ">
        <div className=" px-4">频道号</div>
        <div className=" px-4">{item.channelCode}</div>
      </div>


      <div className="h-[56%] flex mt-2  overflow-hidden w-[80%]">
        <div className="flex-1 flex flex-col ">
          <div className="flex-none h-[5%] mt-6">频道封面</div>
          <div className="flex-1  mt-1 mb-1 pt-4 pb-2 h-44">
            <div className="h-full">
              <MediaUpload
                onChange={handleImageUrl}
                initialUrl={formData.coverUrl || 'https://bhb-frontend.bhbcdn.com/static/files/8165e8edd4f649d1b50c3a7a825b7ce1.png'}
                fileType="image"
                onDelete={(deleted) => setIsVideoDeleted(deleted)}
                style={{ ...customStyle, width: '88%', height: '97%' }} 
              />
            </div>
          </div>
          <small className="flex-none h-[6%] text-xs text-gray-400">推荐1:1比例，尺寸800*800px</small>
          <small className="flex-none h-[10%] text-xs text-gray-400">支持格式：jpg, png，文件大小 ＜ 2MB</small>
        </div>


        <div className="flex-1 flex flex-col pl-1">
          <div className="flex-none h-[5%] mt-6">简介</div>
          <div className="flex-1  mt-1 pt-4 pb-2 ">
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                description: e.target.value
              }))}
              placeholder="请输入"
              className=" w-80 h-[78%]"
            />
          </div>
        </div>
      </div>


      <div className="h-[7%] mt-0  flex items-center justify-center">
        <div className="flex-1 px-4 ">
          <Button
            onClick={handleSubmit}
            disabled={uploadProgress > 0}
            className="p-6 pl-8 pr-8"
          >
            {uploadProgress > 0 ? `上传中 ${uploadProgress}%` : '保存'}
          </Button>
        </div>
      </div>
    </div>
  );
}