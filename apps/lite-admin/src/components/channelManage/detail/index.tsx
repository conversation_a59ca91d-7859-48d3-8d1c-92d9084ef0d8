import { useState, useRef, useEffect } from 'react';
import { ChannelSetting } from './channelSetting';
import { ClassificationManage } from './classificationManage';
import { ContentManage } from './contentManage';
import { StaffManage } from './staffManage';
import { Crumbs } from './crumbs';
import { ScrollArea } from '@mono/ui/scroll-area';


const tabItems = [
  { key: 'content', label: '内容管理', component: <ContentManage /> },
  { key: 'channel', label: '频道设置', component: <ChannelSetting /> },
  { key: 'staff', label: '人员管理', component: <StaffManage /> },
  { key: 'classification', label: '分类管理', component: <ClassificationManage /> },
];

const MainComponent = () => {
  const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');
  const [activeTab, setActiveTab] = useState<string>('content');
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 });

  const tabRefs = useRef<Record<string, HTMLDivElement>>({});
  useEffect(() => {
    const el = tabRefs.current[activeTab];
    if (el) {
      const { offsetLeft, offsetWidth } = el;
      setIndicatorStyle({ left: offsetLeft - 32, width: offsetWidth });
    }
  }, [activeTab]);

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="p-6">
        <Crumbs />
        <h1 className="text-2xl font-bold mb-10 mt-9">{item.channelName}</h1>
        <div className="relative flex space-x-8 border-b mb-4 pb-2">
          {tabItems.map((item) => (
            <div
              key={item.key}
              ref={(el) => {
                if (el) tabRefs.current[item.key] = el;
              }}
              onClick={() => setActiveTab(item.key)}
              className={`cursor-pointer transition-colors duration-300 ${activeTab === item.key ? 'text-green-600' : ''
                }`}
            >
              {item.label}
            </div>
          ))}
          <div
            className="absolute bottom-0 h-0.5 bg-green-600 transition-all duration-300 ease-in-out"
            style={{
              left: `${indicatorStyle.left}px`,
              width: `${indicatorStyle.width}px`,
            }}
          />
        </div>
        <div>
          {tabItems.find((item) => item.key === activeTab)?.component}
        </div>
      </div>
    </ScrollArea>

  );
};

export default MainComponent;