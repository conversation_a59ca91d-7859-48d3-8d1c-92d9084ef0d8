import { useEffect, useMemo, useState } from "react";
import { SearchCom } from "../searchCom";
import { FetchChannelParams, CreatePost, GetMaterialList, FetchTexts, AddText, FetchCategoryParams, CategoryItem } from '@/types/channelManage';
import { Button } from '@mono/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import { Label } from "@mono/ui/label";
import { RadioGroup, RadioGroupItem } from "@mono/ui/radio-group";
import { LoadingButton } from "@/components/loading-button";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Input } from "@mono/ui/input";
import { Textarea } from '@mono/ui/textarea';
import { CustomSelect } from "@mono/ui/common/CustomSelect";
import { MediaUpload } from "@/components/customizedAICharacter/AdvanceUpload";
import { addMaterial, addTexts, delMaterial, delTexts, fetchContentGategory, fetchMaterial, fetchTexts, updateMaterial, updateTexts } from "@/api/channelManage";//添加 \  获取
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@mono/ui/dropdown-menu";
import uploadFileToOSS from '@/api/uploadFileToOSS '
import { Checkbox } from '@mono/ui/checkbox';
import { Loader2 } from "lucide-react";

export function ContentManage() {
  const item = JSON.parse(sessionStorage.getItem('channelItem') || 'null');
  const [searchParams, setSearchParams] = useState<FetchChannelParams>({
    searchType: 1,
    searchText: undefined,
    startDate: undefined,
    endDate: undefined
  });
  const [typeClass, setTypeClass] = useState<1 | 2>(1); 
  const handleSearch = (params: FetchChannelParams) => {
    setSearchParams(params);
  };


  const [setting, setSetting] = useState(false); 
  const [addTypeClass, setAddTypeClass] = useState<1 | 2>(1); 
  const [addQuantity, setAddQuantity] = useState<'single' | 'batch'>('single'); 
  const [openDialog, setOpenDialog] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<{ url: string; postfix: string }[]>([]);
  const [fixFile, setFixFile] = useState<File[]>([]);
  const [categoryId, setCategoryId] = useState('');
  const [textForm, setTextForm] = useState({
    title: '',
    content: '',
    materialIds: [] as string[],
    fileUrl: ''
  })

  const [showMaterialSelector, setShowMaterialSelector] = useState(false);
  const [selectedMaterials, setSelectedMaterials] = useState<{
    id: string;
    fileUrl: string;
  }[]>([]);
console.log(selectedMaterials);

  const [previewMaterials, setPreviewMaterials] = useState<{
    id: string;
    fileUrl: string;
  }[]>([]);

  const pagination = ({
    pageIndex: 0,
    pageSize: 10,
  });

  const { data: materialData } = useQuery<GetMaterialList>({
    queryKey: ['fetchMaterial', item?._id, pagination, searchParams],
    queryFn: () => {
      const finalParams = {
        ...searchParams,
        channelId: item?._id,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        totalSize: pagination.pageSize,
        totalPage: pagination.pageIndex,
        data: []
      };
      return fetchMaterial(finalParams);
    },
    enabled: !!item?._id
  });

  const { data: textData } = useQuery<FetchTexts>({
    queryKey: ['fetchTexts', item?._id, pagination, searchParams],
    queryFn: () => {
      const finalParams = {
        ...searchParams,
        channelId: item?._id,
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        totalSize: pagination.pageSize,
        totalPage: pagination.pageIndex,
        data: []
      };
      return fetchTexts(finalParams);
    },
    enabled: !!item?._id
  });

  const queryClient = useQueryClient();
  const saveMutation = useMutation({
    mutationFn: addMaterial,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchMaterial'] });
      setUploadedFiles([]);
      setCategoryId('');
      setOpenDialog(false);
    },
    onError: (error) => {
      toast.error(`新增失败: ${error.message}`);
    }
  });

  const saveTextMutation = useMutation({
    mutationFn: addTexts,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fetchTexts'] });
      setTextForm({
        title: '',
        content: '',
        materialIds: [],
        fileUrl: ''
      });
      setCategoryId('');
      setSelectedMaterials([]);
      setOpenDialog(false);
    },
    onError: (error) => {
      toast.error(`文案新增失败: ${error.message}`);
    }
  });

  // 多文件上传,但ui这块要排列一下
  const handleFileUpload = (files?: File | File[] | FileList) => {
    if (!files) return;
    let fileArray: File[] = [];
    if (files instanceof File) {
      fileArray = [files];
    } else if ('length' in files) {
      fileArray = Array.from(files as FileList);
    } else {
      fileArray = files;
    }
    setFixFile(fileArray)
    const newFiles = fileArray.map(file => ({
      url: URL.createObjectURL(file),
      postfix: file.name.split('.').pop() || ''
    }));
    setUploadedFiles(prev => [...prev, ...newFiles]);
  };

  const handleBindMaterial = (materialId: string, fileUrl: string) => {
    setPreviewMaterials(prev => {
      const existingIndex = prev.findIndex(item => item.id === materialId);
      if (existingIndex >= 0) {
        return prev.filter(item => item.id !== materialId);
      } else {
        return [...prev, { id: materialId, fileUrl }];
      }
    });
  };

  const confirmMaterialSelection = () => {
    setTextForm(prev => ({
      ...prev,
      materialIds: previewMaterials.map(m => m.id),
      fileUrl: previewMaterials[0]?.fileUrl || '' 
    }));
    setShowMaterialSelector(false);
  };

  const handleSubmit = async () => {
    if (addTypeClass === 2) { 
      if (uploadedFiles.length === 0) {
        toast.warning('请上传至少一个文件');
        return;
      }

      try {
        const ossResults = await Promise.all(
          fixFile.map(async (file) => {
            const url = await uploadFileToOSS(file);
            return {
              url,
              postfix: file.name.split('.').pop() || ''
            };
          })
        );

        const params: CreatePost = {
          channelId: item._id,
          isMulti: addQuantity === 'batch',
          files: ossResults,
          categoryId: categoryId
        };

        saveMutation.mutate(params);
        toast.success('素材新增成功!')
      } catch (error) {
        toast.error(`上传失败: ${(error as Error).message}`);
      }

    } else if (addTypeClass === 1) {

      if (!textForm.title.trim()) {
        toast.warning('请输入文案标题');
        return;
      }
      if (!textForm.content.trim()) {
        toast.warning('请输入文案内容');
        return;
      }

      const params: AddText = {
        channelId: item._id,
        isMulti: addQuantity === 'batch',
        title: textForm.title,
        content: textForm.content,
        materialIds: textForm.materialIds,
        fileUrl: textForm.fileUrl,
        categoryId: categoryId || undefined
      };

      saveTextMutation.mutate(params);
      toast.success('文案新增成功!')
    } else {
      toast.error('新增失败!')
    }
  };

  const deleteMutation = useMutation({
    mutationFn: ({ type, ids }: { type: 'text' | 'material'; ids: string[] }) => {
      if (type === 'text') {
        return delTexts({ ids });
      } else {
        console.log(ids);

        return delMaterial({ ids });
      }
    },
    onSuccess: (_, variables) => {
      toast.success(`${variables.type === 'text' ? '文案' : '素材'}删除成功`);
      if (variables.type === 'text') {
        queryClient.invalidateQueries({ queryKey: ['fetchTexts'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['fetchMaterial'] });
      }
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`);
    }
  });

  const handleDelete = (type: 'text' | 'material', id: string) => {
    if (window.confirm('确定要删除吗？')) {
      deleteMutation.mutate({ type, ids: [id] }); 
    }
  };

  const customStyle = {
    cursor: 'pointer',
    borderRadius: '0.5rem',
    border: '1px dashed #6ee7b7',
    maxHeight: '100%',
    maxWidth: '100%',
  };

  const { data: categoryData } = useQuery({
    queryKey: ['fetchContentGategory', item?._id, pagination, searchParams],
    queryFn: () => {
      const finalParams: FetchCategoryParams = {
        ...searchParams,
        channelId: item?._id ?? '', 
        page: pagination.pageIndex + 1,
        pageSize: pagination.pageSize,
        searchType: searchParams.searchType ?? 1,
        startDate: typeof searchParams.startDate === 'string'
          ? new Date(searchParams.startDate).getTime()
          : searchParams.startDate ?? Date.now() - 365 * 23 * 59 * 59 * 1000,
        endDate: typeof searchParams.endDate === 'string'
          ? new Date(searchParams.endDate).getTime()
          : searchParams.endDate ?? Date.now(),
      };
      return fetchContentGategory(finalParams);
    },
    enabled: !!item?._id,
  });

  const filteredOptions = useMemo(() => {
    if (!categoryData || !Array.isArray(categoryData)) {
      return [];
    }
    let targetType: 1 | 2 | null = null;
    if (addTypeClass === 1) {
      targetType = 1;
    } else if (addTypeClass === 2) {
      targetType = 2;
    } else {
      return [];
    }

    return categoryData
      .filter((item: CategoryItem) => item.type === targetType && item.status === 1)
      .map((item: CategoryItem) => ({
        label: item.name,
        value: item._id,
      }));
  }, [addTypeClass, categoryData]);


  useEffect(() => {
    if (filteredOptions.length > 0 && !categoryId) {
      setCategoryId(filteredOptions[0].value);
    }
  }, [filteredOptions]);

  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingItems, setEditingItems] = useState<{
    ids: string[];
    type: 'text' | 'material';
    currentCategoryId?: string;
  }>({ ids: [], type: 'text' });
  const [selectedCategoryId, setSelectedCategoryId] = useState('');

  const updateCategoryMutation = useMutation({
    mutationFn: (params: { type: 'text' | 'material'; ids: string[]; categoryId: string }) => {
      if (params.type === 'text') {
        return updateTexts({
          ids: params.ids,
          categoryId: params.categoryId
        });
      } else {
        return updateMaterial({
          ids: params.ids,
          categoryId: params.categoryId
        });
      }
    },
    onSuccess: (_, variables) => {
      toast.success(`分类修改成功`);
      if (variables.type === 'text') {
        queryClient.invalidateQueries({ queryKey: ['fetchTexts'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['fetchMaterial'] });
      }
    },
    onError: (error) => {
      toast.error(`分类修改失败: ${error.message}`);
    }
  });

  return (
    <div>
      {/* 切换按钮组 */}
      <div className="flex items-center gap-4 mb-6">
        <button
          onClick={() => setTypeClass(1)}
          className={`px-4 py-2 rounded-3xl transition-colors ${typeClass === 1
            ? ' text-green-600 border border-green-600 bg-green-550'
            : 'bg-gray-100 text-gray-700 border border-gray-200'
            }`}
        >
          文案库
        </button>

        <button
          onClick={() => setTypeClass(2)}
          className={`px-4 py-2 rounded-3xl transition-colors ${typeClass === 2
            ? ' text-green-600 border border-green-600'
            : 'bg-gray-100 text-gray-700 border border-gray-200'
            }`}
        >
          素材库
        </button>
      </div>

      {/* 搜索和操作区域 */}
      <div className="flex items-center justify-between mb-4">
        <SearchCom onSearch={handleSearch} />

        <div className="gap-2 flex mt-4">
          <Button variant="outline">导出数据</Button>
          {typeClass === 1 && <Button variant="outline">导出记录</Button>}


          <Dialog open={openDialog} onOpenChange={setOpenDialog}>
            <DialogTrigger asChild>
              <Button
                variant="default"
                onClick={() => {
                  setAddTypeClass(typeClass);
                  setOpenDialog(true);
                }}
              >
                新增
              </Button>
            </DialogTrigger>
            {/* h-screen */}

            <DialogContent className="!max-w-none w-[70%] p-[16px] h-screen mt-1 pb-0 ">
              <DialogHeader className="border-b pb-2  w-full ">
                <div className="text-center ">新增内容</div>
              </DialogHeader>

              <div className="flex flex-col gap-2  ml-10 mt-0 pb-4 overflow-y-auto" >
                <div className="flex flex-col ">

                  <div className="h-[50px] flex flex-col justify-between ">
                    <small className="">上传类型</small>
                    <RadioGroup
                      className="flex "
                      value={String(addTypeClass)}
                      onValueChange={(val) => setAddTypeClass(Number(val) as 1 | 2)}
                    >
                      <div className="flex gap-2">
                        <RadioGroupItem value="1" id="copy" />
                        <Label htmlFor="copy">文案库</Label>
                      </div>
                      <div className="flex ml-4 gap-2">
                        <RadioGroupItem value="2" id="material" />
                        <Label htmlFor="material">素材库</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="h-[70px] flex flex-col justify-between mt-4">
                    <small className="">上传方式</small>
                    <RadioGroup
                      className="flex "
                      value={String(addQuantity)}
                      onValueChange={(val) => setAddQuantity(val as 'single' | 'batch')}
                    >
                      <div className="flex gap-2">
                        <RadioGroupItem value="single" id="copy" />
                        <Label htmlFor="copy">单个</Label>
                      </div>
                      <div className="flex ml-4 gap-2">
                        <RadioGroupItem value="batch" id="material" />
                        <Label htmlFor="material">批量</Label>
                      </div>
                    </RadioGroup>

                  </div>


                  {/* 第三个大盒子[文案拥有] */}
                  {addTypeClass === 1 && addQuantity == 'single' ? (
                    <>
                      <div className="h-[65px] flex flex-col mt-3 justify-between">
                        <div className="flex">
                          <img src={RequiredIco} className="w-[14px] h-[14px]" />
                          <small className="h-1/2 ">文案标题</small>
                        </div>
                        <div className="h-1/2 w-[98%] ml-1">
                          <Input
                            value={textForm.title}
                            onChange={(e) => setTextForm({ ...textForm, title: e.target.value })}
                          />
                        </div>
                      </div>

                      {/* 第四个大盒子[文案拥有] */}
                      <div className="h-[320px] flex flex-col mt-7 justify-between">
                        <div className="flex">
                          <img src={RequiredIco} className="w-[14px] h-[14px]" />
                          <small className="h-[9%] ">文案内容</small>
                        </div>
                        <div className="h-[60%] ">
                          <Textarea
                            className="h-[100%] w-[98%] ml-1"
                            value={textForm.content}
                            onChange={(e) => setTextForm({ ...textForm, content: e.target.value })}
                          />
                        </div>
                        <small className="h-[25%] text-xs text-gray-500 ml-1">输入 "/" 可使用变量，用户使用时将自动根据资料卡替换内容</small>
                      </div>

                    </>
                  ) : (
                    <div className="h-[320px] flex flex-col mt-7 justify-between ">
                      <div className="flex">
                        <img src={RequiredIco} className="w-[14px] h-[14px]" />
                        <small className="h-[9%] ">上传素材</small>
                      </div>
                      <div className="h-[70%] ">
                        <div className="h-full">
                          <MediaUpload
                            onChange={handleFileUpload}
                            fileType="image"
                            style={{ ...customStyle, width: '95%', height: '97%' }}
                          />
                        </div>
                      </div>
                      <small className="h-[15%] text-xs text-gray-500 ml-1">*单个文件大小不能超过200MB</small>
                    </div>
                  )}

                  {addTypeClass === 1 ? (
                    <div className="h-[10px] flex items-start mt-[-40px] flex-col">

                      {/* 绑定素材弹窗 */}
                      <Dialog open={showMaterialSelector} onOpenChange={setShowMaterialSelector}>
                        <DialogTrigger >
                          <Button
                            variant='ghost'
                            className="border w-[90px] border-gray-200"
                            onClick={() => setShowMaterialSelector(true)}
                          >
                            绑定素材
                          </Button>
                          {textForm.materialIds.length > 0 && (
                            <small className="ml-2 text-sm text-gray-500">
                              已绑定 {textForm.materialIds.length} 个素材
                            </small>
                          )}

                          {/* 预览选中的素材 */}
                          {textForm.materialIds.length > 0 && (
                            <div className="flex gap-2 w-[100%] h-[200px] flex-wrap mt-2">
                              {previewMaterials.map(material => (
                                <img
                                  key={material.id}
                                  src={material.fileUrl}
                                  className="rounded-lg w-[87px] h-[140px] object-cover border border-gray-200"
                                  alt="选中素材"
                                />
                              ))}
                            </div>
                          )}

                        </DialogTrigger>

                        <DialogContent className="w-[87vw] max-w-none  h-[100%]">
                          <DialogHeader className="border-b border-gray-200 flex items-center h-11">
                            素材绑定
                          </DialogHeader>

                          {/* 素材内容选择 */}
                          <div className="flex flex-col gap-2 ml-10 mt-0 pb-4 overflow-y-auto">
                            <div className="flex w-full gap-6 flex-wrap pt-2">
                              {materialData?.data.map((cardMaterial, index) => {
                                const isSelected = previewMaterials.some(m => m.id === cardMaterial._id);
                                return (
                                  <div key={cardMaterial._id} className="flex-col">
                                    <div
                                      className={`relative w-[220px] h-[370px] bg-black border rounded-lg overflow-hidden flex items-center justify-center cursor-pointer ${isSelected ? 'ring-2 ring-green-500' : ''
                                        }`}
                                      onClick={() => handleBindMaterial(cardMaterial._id, cardMaterial.fileUrl)}
                                    >
                                      <img src={cardMaterial.fileUrl} className="max-w-full max-h-full object-contain" />
                                      <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white flex justify-between items-center">
                                        <small className="bg-green-50 w-16 pl-2.5 text-black p-1 rounded-br-lg mt-[-10px]">
                                          编号: {Number(materialData?.data.length) - index}
                                        </small>
                                        <Checkbox
                                          className="mr-2 border-gray-200 w-5 h-5 mt-2"
                                          checked={isSelected}
                                          onCheckedChange={() => handleBindMaterial(cardMaterial._id, cardMaterial.fileUrl)}
                                        />
                                      </div>
                                    </div>
                                    {(() => {
                                      const category = categoryData?.find(
                                        (item: CategoryItem) => item._id === cardMaterial.categoryId
                                      );
                                      if (!category?.name) return null;
                                      return (
                                        <small className="block mt-1 text-xs text-gray-500">
                                          · {category.name}
                                        </small>
                                      );
                                    })()}
                                  </div>
                                );
                              })}
                            </div>
                          </div>

                          <DialogFooter className='border-t pt-4'>
                            <Button
                              variant="ghost"
                              className='border border-gray-300'
                              onClick={() => setShowMaterialSelector(false)}
                            >
                              取消
                            </Button>
                            <LoadingButton onClick={confirmMaterialSelection}>
                              确认选择
                            </LoadingButton>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  ) : (<></>)}

                  <div className="h-[80px] flex mt-44 items-start w-[30%]">
                    <div className="w-1/4 mt-1.5">分类</div>
                    <div className="w-3/4">
                      <CustomSelect
                        placeholder="分类选择"
                        value={categoryId}
                        onChange={(value) => setCategoryId(value as string)}
                        options={filteredOptions} 
                        className="w-32 mr-2"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter className='border-t pt-4'>
                <Button variant="ghost" className='border border-gray-300'
                  onClick={() => setOpenDialog(false)}
                >
                  取消
                </Button>
                <LoadingButton
                  onClick={handleSubmit}
                  disabled={saveMutation.isPending}
                  className="hover:bg-primary/90"
                >
                  {saveMutation.isPending&&<Loader2 className="h-4 w-4 animate-spin"/>}
                  提交
                </LoadingButton>

              </DialogFooter>
            </DialogContent>
          </Dialog>

        </div>
      </div>

      <div className="gap-2 flex mt-4">
        {typeClass === 1 &&
          <Dialog >
            {/* 没有文案变量接口 */}
            <DialogTrigger asChild>
              <Button size="sm" className="border border-gray-200 " variant='outline' >
                文案变量
              </Button>
            </DialogTrigger>


            <DialogContent className="!max-w-none w-[60%]  p-6 pt-2 pb-0">
              <DialogHeader className="pt-7  pb-3 mb-0 w-full items-center mt-[-20px] border-b border-gray-200 justify-center text-center">
                <div>文案变量管理</div>
              </DialogHeader>

              {/* 中心功能 */}
              <div className=" w-full p-4 pb-0 flex pt-0 flex-col gap-4">
                {/* 类型功能 */}
                <SearchCom onSearch={handleSearch} />

                <div className=" h-[20%] flex flex-col   rounded-lg p-4 pb-0 pt-0 ">
                  <div className="h-[90%]  items-center">
                    <span className="text-sm font-medium">频道类型</span>
                  </div>
                </div>
              </div>

              <DialogFooter className="pb-5">
                <Button variant="ghost" className='border border-gray-300 w-20 ' >
                  取消
                </Button>
                <LoadingButton className="w-20" >
                  继续
                </LoadingButton>

              </DialogFooter>
            </DialogContent>

          </Dialog>}
        {setting == false ? (
          <Button variant="outline" className="w-18 h-8 text-xs" onClick={() => setSetting(true)}>
            操作
          </Button>

        ) : (
          <>
            <Button variant="outline" onClick={() => setSetting(false)} className="w-18 h-8 text-xs">取消</Button>
            <span className="text-sm mt-1.5">全选当前页</span>
          </>

        )}
      </div>

      <div className="mt-6">
        {typeClass === 1 ? (
          // 文案区
          <div>
            <div className="flex w-full gap-6 flex-wrap">

              {textData?.data.map((cardText, index) => (
                <div className="flex-col">
                  <div
                    className="relative w-[270px] h-[370px] bg-slate-50 rounded-lg flex items-center 
                              transition-all duration-300 ease-in-out flex-col
                              cursor-pointer hover:-translate-y-2 hover:shadow-lg overflow-y-auto 
                               dark:border dark:bg-zinc-900"
                  >
                    <div className="absolute top-0 left-0 right-0  bg-opacity-50 text-white  flex justify-between items-center">
                      <small className="bg-green-50  w-16 pl-2.5 text-black p-1  rounded-br-lg mt-[-10px]">
                        编号: {Number(textData?.data.length) - index}
                      </small>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="mr-2 text-black dark:text-white">
                            ⋯
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent side="bottom" className="px-1 py-1 w-20">
                          {/* 修改分类选项 */}
                          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                            <DialogTrigger asChild>
                              <Button variant="ghost"
                                onClick={() => {
                                  setEditingItems({
                                    ids: [cardText._id],
                                    type: 'text',
                                    currentCategoryId: cardText.categoryId
                                  });
                                  setSelectedCategoryId(cardText.categoryId || '');
                                  setEditDialogOpen(true);
                                }}
                                className="h-[33px] rounded-sm w-full flex items-center justify-center cursor-pointer">
                                修改分类
                              </Button>
                            </DialogTrigger>

                            <DialogContent>
                              <DialogHeader className="border-b border-gray-200 pb-2 flex  items-center">更改分类</DialogHeader>

                              <div className="h-[80px] flex justify-center text-center items-center w-[100%]">
                                <div className="w-[20%] mt-1.5">分类</div>
                                <div className="w-[60%]">
                                  <CustomSelect
                                    value={selectedCategoryId}
                                    onChange={(value) => setSelectedCategoryId(value as string)}
                                    options={
                                      (categoryData || [])
                                        .filter((item: CategoryItem) =>
                                          item.type === (editingItems.type === 'text' ? 1 : 2) &&
                                          item.status === 1
                                        )
                                        .map((item: CategoryItem) => ({
                                          label: item.name,
                                          value: item._id,
                                        }))
                                    }
                                  />
                                </div>
                              </div>

                              <DialogFooter className="border-t pt-4">
                                <Button
                                  variant="ghost"
                                  className="border border-gray-300"
                                  onClick={() => setEditDialogOpen(false)}
                                >
                                  取消
                                </Button>
                                <LoadingButton onClick={() => {
                                  if (!selectedCategoryId) {
                                    toast.warning('请选择分类');
                                    return;
                                  }
                                  updateCategoryMutation.mutate({
                                    type: editingItems.type,
                                    ids: editingItems.ids,
                                    categoryId: selectedCategoryId
                                  });
                                  setEditDialogOpen(false)
                                }}>提交</LoadingButton>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          {/* 删除选项 */}
                          <DropdownMenuItem
                            className="h-[33px] rounded-sm flex items-center justify-center cursor-pointer"
                            onClick={() => handleDelete('text', cardText._id)}
                          >
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div className="h-72 w-full dark:text-gray-300 flex-col justify-end mt-8">
                      <div className="p-4 border-b w-full  border-gray-200 dark:border-zinc-800 font-medium">{cardText.title}</div>
                      <div className="p-4 text-sm text-gray-600 dark:text-gray-300">{cardText.content}</div>


                    </div>

                    <div className="p-4 dark:text-gray-300 flex gap-3 w-full">
                      {cardText.materialIds.length > 0 ? (
                        cardText.materialIds.map((materialId) => {
                          const material = materialData?.data.find(m => m._id === materialId);
                          if (!material || !material.fileUrl) {
                            return null;
                          }

                          return (
                            <img
                              key={materialId}
                              src={material.fileUrl}
                              alt={`素材 ${materialId}`}
                              className="w-[44px] h-[80px] rounded-lg object-cover mb-2"
                            />
                          );
                        })
                      ) : (
                        // <img
                        //   src="https://placeholder.im/300x200/cccccc"
                        //   // src="https://placeholder.im/300x200/cccccc"
                        //   className="w-[43px] h-[80px] rounded-lg"
                        //   alt="placeholder..."
                        // />
                        <></>
                      )}
                    </div>
                  </div>

                  {(() => {
                    const category = categoryData?.find(
                      (item: CategoryItem) => item._id === cardText.categoryId
                    );

                    if (!category?.name) return null;
                    return (
                      <small className="block mt-1 text-xs text-gray-500">
                        · {category.name}
                      </small>
                    );
                  })()}
                </div>
              ))}
            </div>

          </div>
        ) : (
          <div>
            <div className="flex w-full gap-6 flex-wrap">

              {materialData?.data.map((cardMaterial, index) => (
                <div className="flex-col">
                  <div className="relative w-[220px] h-[370px] 
                      bg-black border rounded-lg overflow-hidden 
                      flex items-center justify-center  transition-all 
                      duration-300 ease-in-out 
                      cursor-pointer hover:-translate-y-2 hover:shadow-lg">
                    <img src={cardMaterial?.fileUrl} className="max-w-full max-h-full object-contain" />
                    <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 text-white  flex justify-between items-center">
                      <small className="bg-green-50 w-16 pl-2.5 text-black p-1 rounded-br-lg mt-[-10px] ">
                        编号: {Number(materialData?.data.length) - index}
                      </small>
                      <DropdownMenu >
                        <DropdownMenuTrigger asChild className="border-none">
                          <Button
                            variant="ghost"
                            className="mr-2  dark:text-white"
                          >
                            ⋯
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent side="bottom" className="px-1  py-1 w-20">
                          {/* 素材修改分类 */}
                          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
                            <DialogTrigger asChild>
                              <Button variant="ghost"
                                onClick={() => {
                                  setEditingItems({
                                    ids: [cardMaterial._id],
                                    type: 'material',
                                    currentCategoryId: cardMaterial.categoryId
                                  });
                                  setSelectedCategoryId(cardMaterial.categoryId || '');
                                  setEditDialogOpen(true);
                                }}
                                className="h-[33px] rounded-sm w-full flex items-center justify-center cursor-pointer">
                                修改分类
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader className="border-b border-gray-200 pb-2 flex  items-center">更改分类</DialogHeader>

                              <div className="h-[80px] flex justify-center text-center items-center w-[100%]">
                                <div className="w-[20%] mt-1.5">分类</div>
                                <div className="w-[60%]">
                                  <CustomSelect
                                    value={selectedCategoryId}
                                    onChange={(value) => setSelectedCategoryId(value as string)}
                                    options={
                                      (categoryData || [])
                                        .filter((item: CategoryItem) =>
                                          item.type === (editingItems.type === 'text' ? 1 : 2) &&
                                          item.status === 1
                                        )
                                        .map((item: CategoryItem) => ({
                                          label: item.name,
                                          value: item._id,
                                        }))
                                    }
                                  />
                                </div>
                              </div>

                              <DialogFooter className="border-t pt-4">
                                <Button
                                  variant="ghost"
                                  className="border border-gray-300"
                                  onClick={() => setEditDialogOpen(false)}
                                >
                                  取消
                                </Button>
                                <LoadingButton onClick={() => {
                                  if (!selectedCategoryId) {
                                    toast.warning('请选择分类');
                                    return;
                                  }
                                  updateCategoryMutation.mutate({
                                    type: editingItems.type,
                                    ids: editingItems.ids,
                                    categoryId: selectedCategoryId
                                  });
                                  setEditDialogOpen(false)
                                }}>提交</LoadingButton>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>

                          <DropdownMenuItem className=" h-[33px]  rounded-sm flex items-center justify-center cursor-pointer"
                            onClick={() => handleDelete('material', cardMaterial._id)}
                          >
                            删除
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>

                  </div>
                  {(() => {
                    const category = categoryData?.find(
                      (item: CategoryItem) => item._id === cardMaterial.categoryId
                    );

                    if (!category?.name) return null;

                    return (
                      <small className="block mt-1 text-xs text-gray-500">
                        · {category.name}
                      </small>
                    );
                  })()}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}