import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@mono/ui/card';
import { Button } from '@mono/ui/button';
import { Link } from '@tanstack/react-router';
import { Globe, Share2, Zap, User } from 'lucide-react';

export function AppManagePage() {
  const apps = [
    {
      title: '极速克隆',
      description: '快速克隆和复制功能管理',
      icon: <Zap className="w-8 h-8" />,
      path: '/fastClone',
      color: 'text-blue-600',
    },
    {
      title: '定制数字人',
      description: '数字人定制和管理功能',
      icon: <User className="w-8 h-8" />,
      path: '/customizedAICharacter',
      color: 'text-green-600',
    },
    {
      title: '声音克隆',
      description: '声音克隆和音频处理管理',
      icon: <Share2 className="w-8 h-8" />,
      path: '/soundClone',
      color: 'text-purple-600',
    },
    {
      title: '频道管理',
      description: '频道配置和管理功能',
      icon: <Globe className="w-8 h-8" />,
      path: '/channelManage',
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">应用管理</h1>
        <p className="text-muted-foreground mt-2">
          管理和配置各种应用功能模块
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {apps.map((app) => (
          <Card key={app.path} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className={`${app.color}`}>
                  {app.icon}
                </div>
                <div>
                  <CardTitle className="text-xl">{app.title}</CardTitle>
                  <CardDescription className="mt-1">
                    {app.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Link to={app.path}>
                <Button className="w-full">
                  进入管理
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
