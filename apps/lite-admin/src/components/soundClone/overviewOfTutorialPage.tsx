import { useEffect, useState } from "react";
import { WechatMiniAppPreview } from "../WechatMiniAppPreview";
import { soundCloneParams } from '@/types/soundClone';

export function TutorialPreview({
  values,
}: {
  values: soundCloneParams[]
}) {
  const [filteredData, setFilteredData] = useState<soundCloneParams[]>([]);

  useEffect(() => {
    const processData = (input: any[]): soundCloneParams[] => {
      if (input.length > 0 && input[0] && typeof input[0] === 'object' && !Array.isArray(input[0])) {
        const firstItem = input[0];
        const hasNumberKeys = Object.keys(firstItem).some(key => !isNaN(Number(key)));
        if (hasNumberKeys) {
          return Object.keys(firstItem)
            .filter(key => !isNaN(Number(key)))
            .map(key => ({
              typeName: firstItem[key].typeName || '未分类',
              content: firstItem[key].content,
              voiceUrl: firstItem[key].voiceUrl,
              voiceSort: firstItem[key].voiceSort,
              _id: firstItem[key]._id
            }));
        }
      }
      return input.map(item => ({
        typeName: item.typeName || '未分类',
        content: item.content,
        voiceUrl: item.voiceUrl,
        voiceSort: item.voiceSort,
        _id: item._id
      }));
    };

    setFilteredData(processData(values));
  }, [values]);
  
  return (
    <div className="rounded-lg p-4 shadow-sm h-full">
      <div className="rounded-md h-[calc(100%-2rem)] flex items-center justify-center">
        <div className='flex-1 mt-[-15px]'>
          <WechatMiniAppPreview 
            previewType="soundClone" 
            soundData={filteredData} 
          />
        </div>
      </div>
    </div>
  );
}