import { ScrollArea } from '@mono/ui/scroll-area';
import { useEffect, useState } from 'react';
import { ReferenceCopy } from './referenceCopy';
import { TutorialPreview } from './overviewOfTutorialPage.tsx';
import { fetchVoiceTemp, saveVoiceTemp } from '@/api/soundClone';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { soundCloneParams } from '@/types/soundClone';
import { toast } from 'sonner';
import { LoadingContainer } from '../loading.tsx';

export function SoundClone() {

  const [soundCloneArr, setSoundCloneArr] = useState<soundCloneParams[]>([]);
  const queryClient = useQueryClient();

  const queryParams = {
    typeName: '',
    content: '',
    voiceUrl: '',
    isNew: undefined,
    _id: '',
    voiceSort: undefined,
  };

  const { data, isLoading } = useQuery({
    queryKey: ['saveVoiceTemp', queryParams],
    queryFn: async () => {
      const res = await fetchVoiceTemp(queryParams);
      return res || [];
    },
  });

  const saveMutation = useMutation({
    mutationFn: saveVoiceTemp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['saveVoiceTemp'] });
      toast.success('保存成功');
    },
    onError: (error) => {
      toast.error(`保存失败: ${error.message}`);
    }
  });


  useEffect(() => {
    if (!data) return;
    const dataArray = Array.isArray(data) ? data : [data];
    setSoundCloneArr(
      dataArray.map(item => ({
        ...item,
        isNew: false
      }))
    );
  }, [data]);

  const handleSave = (newData: soundCloneParams[]) => {

    saveMutation.mutate(newData);
  };

  const handleDataChange = (newData: soundCloneParams[]) => {
    setSoundCloneArr(newData);
  };

  if (isLoading) {
    return (
      <LoadingContainer/>
    );
  }


  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="pl-2 pt-4">
        <div className="text-2xl font-bold pl-3">声音克隆</div>
        <div className="h-full flex flex-col">
          <div className="px-6 py-4 border-b flex items-center justify-between pl-0 w-[1000px]">
            <span className="text-lg font-semibold pl-3">参考文案</span>
            <span className="text-lg font-semibold w-[35%]">教程页面概览</span>
          </div>
          <div className="flex h-0 flex-1 overflow-hidden">
            <div className="w-[calc(45%_-_0.75rem)] pl-2 pr-8 h-full flex flex-col mr-4">
              <ReferenceCopy
                values={soundCloneArr}
                onDataChange={handleDataChange}
                onSave={handleSave}
              />
            </div>
            <div className="w-[calc(35%_-_0.75rem)] pl-14 pr-2 h-full pt-4">
              <TutorialPreview values={soundCloneArr} />
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
}