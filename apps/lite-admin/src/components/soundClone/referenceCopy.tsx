import { useState, useEffect } from 'react';
import { ScrollArea } from '@mono/ui/scroll-area';
import { Button } from '@mono/ui/button';
import { soundCloneParams } from '@/types/soundClone';
import { Input } from '@mono/ui/input';
import { Textarea } from '@mono/ui/textarea';
import { toast } from 'sonner';
import { saveVoiceTemps } from '@/api/soundClone';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@mono/ui/dialog';
import { LoadingButton } from '../loading-button';


interface ReferenceCopyProps {
  values: soundCloneParams[];
  onDataChange: (data: soundCloneParams[]) => void;
  onSave: (data: soundCloneParams[]) => void;
}

export function ReferenceCopy({
  values,
  onDataChange,
  // onSave
}: ReferenceCopyProps) {
  const [localData, setLocalData] = useState<soundCloneParams[]>([]);
  const [pendingUploads, setPendingUploads] = useState<Record<string, File | null>>({});
console.log(pendingUploads);

  // 默认数据
  const [defaultTemplates] = useState<soundCloneParams[]>([
    {
      _id: 'default-1',
      voiceSort: 1,
      typeName: '哲学类',
      content: '浩瀚宇宙中，生命如微尘般渺小，却承载着对存在意义的无尽探寻。',
      voiceUrl: '',
      isNew: true,
    },
    {
      _id: 'default-2',
      voiceSort: 2,
      typeName: '抽象类',
      content: '时间如一条无形的河流，悄然流淌，带走一切可见与不可见的存在。',
      voiceUrl: '',
      isNew: true,
    },
    {
      _id: 'default-3',
      voiceSort: 3,
      typeName: '情感类',
      content: '在人生的旅途中，我们会遇到无数次的相遇与别离，每一次都是独特的体验。',
      voiceUrl: '',
      isNew: true,
    },
    {
      _id: 'default-4',
      voiceSort: 4,
      typeName: '励志类',
      content: '成功不是终点，失败也非终结，唯有勇气继续前行才是最重要的。',
      voiceUrl: '',
      isNew: true,
    }
  ]);

  useEffect(() => {
    const flattenedValues = values.flat();
    setLocalData([...flattenedValues]);
  }, [values]);
  const [defaultContent, setdDefaultContent] = useState(false);


  const handleDelete = (index: number) => {
    const newItems = [...localData];
    const itemId = newItems[index]._id;
    setPendingUploads(prev => ({ ...prev, [itemId]: null }));
    newItems.splice(index, 1);
    setLocalData(newItems);
    onDataChange(newItems);
  };

  const handleMoveUp = (index: number) => {
    if (index <= 0) return;

    const newItems = [...localData];
    const currentSort = newItems[index].voiceSort || 0;
    const prevSort = newItems[index - 1].voiceSort || 0;

    newItems[index].voiceSort = prevSort;
    newItems[index - 1].voiceSort = currentSort;

    [newItems[index], newItems[index - 1]] = [newItems[index - 1], newItems[index]];

    setLocalData(newItems);
    onDataChange(newItems);
  };

  const handleAdd = () => {
    if (localData.length >= 5) {
      toast.warning('最多支持新建5组文案');
      return;
    }

    const newItem: soundCloneParams = {
      _id: `temp-${Date.now()}`,
      voiceSort: localData.length > 0
        ? Math.max(...localData.map(item => item.voiceSort || 0)) + 1
        : 1,
      typeName: '',
      content: '',
      voiceUrl: '',
      isNew: true,
    };

    const newItems = [...localData, newItem];
    setLocalData(newItems);
    onDataChange(newItems);
  };
  const handleClearAll = () => {
    setPendingUploads({});

    setLocalData([]);
    onDataChange([]);
     setLocalData([...defaultTemplates]);
    onDataChange([...defaultTemplates]);

    setdDefaultContent(false)
  };


  const handleSave = async () => {
    for (const item of localData) {
      if (!item.typeName.trim() || !item.content.trim()) {
        toast.error('请填写所有必填字段（文案类型和文案正文）');
        return;
      }
    }

    try {
      await saveVoiceTemps(localData);
      const cleanedData = localData.map(item => {
        const { ...rest } = item;
        // const { pendingFile, ...rest } = item;
        return rest;
      });

      setLocalData(cleanedData);
      toast.success('保存成功');
    } catch (error) {
      toast.error('保存失败');
      console.error(error);
    }
  };

  const handleInputChange = (index: number, field: keyof soundCloneParams, value: string) => {
    const newItems = [...localData];
    newItems[index] = { ...newItems[index], [field]: value };
    setLocalData(newItems);
    onDataChange(newItems);
  };

  const handleFileChange = (index: number, file: File | null) => {
    const newItems = [...localData];
    newItems[index] = {
      ...newItems[index],
      voiceUrl: file ? file.name : '',
      pendingFile: file
    };
    setLocalData(newItems);
    onDataChange(newItems);
  };

  return (
    <ScrollArea className="h-full">
      <div className="pl-2 pt-4">
        <div className="flex justify-between items-center space-x-2">
          <div className="space-x-2">
            <Button onClick={handleAdd} size="sm">
              新增文案
            </Button>
            <Dialog  open={defaultContent} onOpenChange={setdDefaultContent}>
              <DialogTrigger asChild>
                <Button size="sm" onClick={()=>setdDefaultContent(true)}>
                   使用默认文案组
                </Button>
              </DialogTrigger>
              <DialogContent className="!max-w-none w-[500px]  p-6">
                <DialogHeader className=" pb-4 mb-4 w-full items-center justify-center text-center">
                  <DialogTitle>提示</DialogTitle>
                </DialogHeader>

                <div className="flex flex-col gap-2 ml-2 mb-4">
                  <div className="flex items-left gap-1 space-y-0 ">
                    默认文案组将覆盖清空所有内容，是否继续
                  </div>
                </div>

                <DialogFooter className=''>
                  <Button variant="ghost" className='border border-gray-300' onClick={()=>setdDefaultContent(false)}>
                    取消
                  </Button>
                  <LoadingButton
                    onClick={handleClearAll}
                  >
                    继续
                  </LoadingButton>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          <Button onClick={handleSave} size="sm"
          
          >
            保存文案
          </Button>
        </div>

        {localData && localData.map((item, index) => (
          <div key={item._id} className="border rounded-lg p-4 mt-4 space-y-4 pb-0 pt-1 pl-3">
            <div className="p-4 rounded-md relative" >
              {/* 文案类型 */}
              <div className="flex items-start mb-4" >
                <span className="text-red-500">*</span>
                <label className="w-24 text-sm font-medium pt-2">文案类型</label>

                <Input
                  value={item.typeName}
                  onChange={(e) => {
                    let inputs = e.target.value;
                    if (inputs.length > 20) {
                      inputs = inputs.slice(0, 20);
                    }
                    handleInputChange(index, 'typeName', inputs)
                  }}
                  placeholder="请输入类型"
                  className="max-w-full min-w-0 rounded-sm focus:bg-white focus:border-blue-500 focus:!ring-0"
                />
              </div>

              {/* 文案正文 */}
              <div className="flex items-start mb-4">
                <span className="text-red-500">*</span>
                <label className="w-24 text-sm font-medium pt-2">文案正文</label>
                <Textarea
                  value={item.content}
                  onChange={(e) => {
                    let input = e.target.value;
                    if (input.length > 200) {
                      input = input.slice(0, 200);
                    }
                    handleInputChange(index, 'content', input);
                  }}
                  placeholder="请输入正文内容"
                  rows={4}
                  className="w-full h-[120px] rounded-sm focus:border-gray-500 focus:!ring-0"
                />
              </div>
              <div className="text-xs text-gray-500 text-right mt-[-33px] pr-2" >
                {item.content.length}/200
              </div>

              {/* 声音示例 */}
              <div className="flex items-center mb-4 pt-4">
                <label className="w-24 text-sm font-medium pl-2">声音示例</label>
                <div className="flex items-center gap-2 w-full h-[25px]">
                  {item.voiceUrl ? (
                    <>
                      <a className='text-sm text-blue-600 underline cursor-pointer pl-2 ml-1 bg-gray-100'>♬</a>
                      <a href={item.voiceUrl} className="text-sm text-gray-500 ml-[-8px] bg-gray-100 cursor-pointer ">
                        {
                          item.voiceUrl && item.voiceUrl.length > 0
                            ? item.voiceUrl.substring(item.voiceUrl.lastIndexOf('/') + 10)
                            : ''
                        }
                      </a>
                      <Button
                        variant="ghost"
                        size="sm"
                        title="删除"
                        onClick={() => handleFileChange(index, null)}
                      >
                        🗑️
                      </Button>
                    </>
                  ) : (
                    <label className="text-sm text-gray-500 underline cursor-pointer pl-1">
                      上传音频
                      <input
                        type="file"
                        className="sr-only"
                        onChange={(e) => handleFileChange(index, e.target.files?.[0] || null)}
                        accept="audio/*"
                      />
                    </label>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="relative right-0 flex justify-end gap-2 ">
                <Button size="sm" onClick={() => handleDelete(index)}>
                  删除
                </Button>
                <Button
                  size="sm"
                  disabled={index === 0}
                  onClick={() => handleMoveUp(index)}
                >
                  移到上一个
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
}