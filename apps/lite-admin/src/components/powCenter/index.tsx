import { fetchCenterHashrateLog } from "@/api/computeDemand";
import { TeamHashrateLogItem, TeamHashrateLogResponse } from "@/types/computeDemand";
import { ColumnDef } from "@tanstack/react-table";
import { formatDate } from '@mono/utils/day';
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { DataTable } from "../dataTable";
import { ScrollArea } from "@mono/ui/scroll-area";
import { Input } from "@mono/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mono/ui/select";
import { Button } from "@mono/ui/button";
import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from "@/components/DatePickerWithRange";
// import { addDays } from "date-fns";

export function ComputeCenterIndex() {
  const [formState, setFormState] = useState({
    searchType: 1,  
    searchText: '',
    changeType: '',
    dateRange: undefined as DateRange | undefined
  });

  const [searchParams, setSearchParams] = useState({
    page: 1,
    size: 10,
    searchType: 1,
    searchText: '',
    changeType: undefined as number | undefined,
    startDate: undefined as number | undefined,
    endDate: undefined as number | undefined
  });

  const { data: centerList } = useQuery<TeamHashrateLogResponse>({
    queryKey: ['fetchCenterHashrateLog', searchParams],
    queryFn: () => fetchCenterHashrateLog({
      page: searchParams.page,
      size: searchParams.size,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      changeType: searchParams.changeType,
      startDate: searchParams.startDate?.toString() || '',
      endDate: searchParams.endDate?.toString() || ''
    }),
  });

  const handleSearch = () => {
    setSearchParams({
      page: 1,
      size: searchParams.size,
      searchType: formState.searchType,
      searchText: formState.searchText,
      changeType: formState.changeType ? Number(formState.changeType) : undefined,
      startDate: formState.dateRange?.from?.getTime(),
      endDate: formState.dateRange?.to?.getTime()
    });
  };

  const handleReset = () => {
    setFormState({
      searchType: 1,
      searchText: '',
      changeType: '',
      dateRange: undefined
    });
    setSearchParams({
      page: 1,
      size: 10,
      searchType: 1,
      searchText: '',
      changeType: undefined,
      startDate: undefined,
      endDate: undefined
    });
  };

  const handleDateChange = (range: DateRange | undefined) => {
    setFormState(prev => ({
      ...prev,
      dateRange: range
    }));
  };

  const handlePaginationChange = (page: number, size: number) => {
    setSearchParams(prev => ({
      ...prev,
      page,
      size
    }));
  };

  const getChangeTypeText = (changeType: number): string => {
    switch (changeType) {
      case 1000: return '分配给代理商';
      case 1001: return '生成兑换码';
      case 1002: return '极速算力赠送';
      case 1003: return '用户小程序充值';
      case 1100: return '作废兑换码 算力回退';
      case 1101: return '算力订单退款';
      default: return '未知类型';
    }
  };

  const columns: ColumnDef<TeamHashrateLogItem>[] = [
    {
      header: '流水号',
      accessorKey: '_id',
      cell: ({ row }) => <span className="text-blue-500">{row.original._id}</span>,
    },
    {
      header: '创建时间',
      cell: ({ row }) => (
        <span>
          {formatDate(new Date(row.original.createdAt).getTime())}
        </span>
      ),
    },
    {
      header: '类型',
      cell: ({ row }) => <span>{getChangeTypeText(row.original.changeType)}</span>,
    },
    {
      header: '用户备注',
      cell: ({ row }) => (
        <div className="flex flex-col">
          {row.original.description}
        </div>
      ),
    },
    {
      header: '算力变化',
      cell: ({ row }) => {
        const value = Number(row.original.changeValue);
        const isPositive = value >= 0;
        const displayValue = isPositive ? `+${value}` : value.toString();

        return (
          <span className={isPositive ? 'text-red-500' : 'text-green-500'}>
            {displayValue}
          </span>
        );
      },
    },
    {
      header: '操作',
      cell: () => <span>--</span>,
    },
  ];

  return (
    <ScrollArea className="h-full" enableHorizontal>
      <div className="flex flex-col gap-2 overflow-hidden w-[97.5%] mx-auto">
        <h1 className="text-xl font-bold mb-4">算力中心</h1>

        <div>
          <div>可用算力</div>
          <div className="text-4xl font-bold mb-4 p-4 pl-0">
            {centerList?.data[centerList?.data.length - 1]?.afterValue.toLocaleString()}
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-4 mb-4 p-4 bg-gray-50 rounded-lg dark:bg-black">
          <div className="flex items-center gap-2">
            <Select
              value={String(formState.searchType)}
              onValueChange={(value) => setFormState({
                ...formState,
                searchType: Number(value),
                searchText: ''
              })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="搜索类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">流水号</SelectItem>
              </SelectContent>
            </Select>
            <Input
              placeholder="请输入流水号"
              className="w-48"
              value={formState.searchText}
              onChange={(e) => setFormState({
                ...formState,
                searchText: e.target.value
              })}
            />
          </div>

          <div className="flex items-center gap-2">
            <Select
              value={formState.changeType}
              onValueChange={(value) => setFormState({
                ...formState,
                changeType: value
              })}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="全部类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="1000">分配给代理商</SelectItem>
                <SelectItem value="1001">生成兑换码</SelectItem>
                <SelectItem value="1002">极速算力赠送</SelectItem>
                <SelectItem value="1003">用户小程序充值</SelectItem>
                <SelectItem value="1100">作废兑换码 算力回退</SelectItem>
                <SelectItem value="1101">算力订单退款</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <DatePickerWithRange
              value={formState.dateRange}
              onChange={handleDateChange}
              className="w-64"
            />
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={handleReset}>重置</Button>
            <Button onClick={handleSearch}>搜索</Button>
          </div>
        </div>

        <DataTable
          columns={columns}
          data={centerList?.data || []}
          rowCount={centerList?.totalSize || 0}
          pagination={{
            pageIndex: searchParams.page - 1,
            pageSize: searchParams.size,
          }}
          setPagination={(updater) => {
            const newPagination = typeof updater === 'function'
              ? updater({
                pageIndex: searchParams.page - 1,
                pageSize: searchParams.size
              })
              : updater;
            handlePaginationChange(newPagination.pageIndex + 1, newPagination.pageSize);
          }}
        />
      </div>
    </ScrollArea>
  );
}