import { useState } from "react";
import { DateRange } from "react-day-picker";
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { Input } from "@mono/ui/input";
import { Button } from "@mono/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@mono/ui/select";
import { Label } from "@mono/ui/label";

export function SearchBar({
  onSearch,
  onReset,
}: {
  onSearch: (params: {
    searchType: string;
    searchText: string;
    channel: string;
    type: string;
    dateRange: DateRange | undefined;
  }) => void;
  onReset: () => void;
}) {
  const [searchType, setSearchType] = useState("1"); // 1: 用户手机号, 2: 定制ID, 3: 数字人名称
  const [searchText, setSearchText] = useState("");
  const [channel, setChannel] = useState("");
  const [type, setType] = useState(""); // pro, lite
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const handleSearch = () => {
    onSearch({
      searchType,
      searchText,
      channel,
      type,
      dateRange,
    });
  };

  const handleReset = () => {
    setSearchType("1");
    setSearchText("");
    setChannel("");
    setType("");
    setDateRange(undefined);
    onReset();
  };

  return (
    <div className="flex flex-wrap items-end gap-4 p-4 border rounded-lg mb-4">
      <div className="flex items-center gap-2">
        <Select value={searchType} onValueChange={setSearchType}>
          <Label>条件搜索</Label>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="搜索类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">用户手机号</SelectItem>
            <SelectItem value="2">声音ID</SelectItem>
            <SelectItem value="3">声音名称</SelectItem>
          </SelectContent>
        </Select>
        <Input
          placeholder={
            searchType === "1"
              ? "请输入用户手机号"
              : searchType === "2"
                ? "请输入声音ID"
                : "请输入声音名称"
          }
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="w-[200px]"
        />
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm">用户所属渠道</span>
        <Input
          placeholder="请输入渠道名称"
          value={channel}
          onChange={(e) => setChannel(e.target.value)}
          className="w-[200px]"
        />
      </div>

      <div className="flex items-center gap-2">
        <Select value={type} onValueChange={setType}>
          <Label>类型</Label>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="全部类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部</SelectItem>
            <SelectItem value="pro">专业版</SelectItem>
            <SelectItem value="lite">极速版</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-2">
        <span className="text-sm">交付时间</span>
        <DatePickerWithRange
          className="w-[250px]"
          onChange={setDateRange}
          value={dateRange}
        />
      </div>

      <div className="flex gap-2 ml-auto">
        <Button variant="outline" onClick={handleReset}>
          重置
        </Button>
        <Button onClick={handleSearch}>搜索</Button>
      </div>
    </div>
  );
}