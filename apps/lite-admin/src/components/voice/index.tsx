import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
import { useState, useRef } from "react";
import { DataTable } from "../dataTable";
import { FigureItem, FigureResponse } from "@/types/figure";
import { Button } from "@mono/ui/button";
import { fetchVoice } from "@/api/figure";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@mono/ui/dropdown-menu";
import { formatDate } from '@mono/utils/day';
import { SearchBar } from './search'
import { ScrollArea } from "@mono/ui/scroll-area";

interface SearchParams {
  searchType: string;
  searchText: string;
  startDate?: number;
  endDate?: number;
  type?: string;
  channel?: string;
}
export function VoiceIndex() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [listType, setListType] = useState<1 | 2>(1); // 默认1（定制声音）
  const [selectedVoice, setSelectedVoice] = useState<FigureItem | null>(null); // 新增：选中的声音
  const [showDetail, setShowDetail] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    searchType: '1',
    searchText: '',
    type: '',
    channel: '',
  });

  const queryClient = useQueryClient();

  // 数据获取
const { data: voice } = useQuery<FigureResponse>({
  queryKey: ['fetchVoice', { 
    ...searchParams, 
    listType,
    page: pagination.pageIndex + 1,
    size: pagination.pageSize
  }],
  queryFn: () => fetchVoice({
    ...searchParams,
    listType,
    page: pagination.pageIndex + 1,
    size: pagination.pageSize,
    ...(searchParams.startDate && { startDate: searchParams.startDate }),
    ...(searchParams.endDate && { endDate: searchParams.endDate })
  }),
});

  // 刷新数据
  const resetSearchParams = () => {
    setSearchParams({
      searchType: '1',
      searchText: '',
      startDate: undefined,
      endDate: undefined,
      type: '',
      channel: '',
    });
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  const switchToListType = (type: 1 | 2) => {
    setListType(type);
    resetSearchParams();
  };

  const StatusBadge = ({ status }: { status?: string }) => {
    if (!status) return null;

    return (
      <div className="flex items-center">
        <span
          className={`mr-1 text-lg  ${status === 'unconfirmed' ? 'text-green-500' : 'text-red-500'}`}
        >
          •
        </span>
        <span>
          {status === 'unconfirmed' ? '训练成功' : '训练失败'}
        </span>
      </div>
    );
  };

  const AudioPlayer = ({ demoUrl }: { demoUrl?: string }) => {
    const audioRef = useRef<HTMLAudioElement>(null);
    const [isPlaying, setIsPlaying] = useState(false);

    const togglePlay = () => {
      if (!demoUrl) return;

      if (isPlaying) {
        audioRef.current?.pause();
      } else {
        audioRef.current?.play();
      }
      setIsPlaying(!isPlaying);
    };

    return (
      <div className="flex items-center">
        <audio
          ref={audioRef}
          src={demoUrl}
          onEnded={() => setIsPlaying(false)}
        />

        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlay}
          disabled={!demoUrl}
        >
          {isPlaying ? '停止' : '试听'}
        </Button>
      </div>
    );
  };

  const TypeBadge = ({ type = 'unknown' }: { type?: string }) => {
    const typeConfig = {
      lite: {
        className: "bg-green-50 text-green-600 border border-green-100",
        text: "极速版",
      },
      pro: {
        className: "bg-orange-50 text-orange-600 border border-orange-100",
        text: "专业版",
      },
      unknown: {
        className: "bg-gray-100 text-gray-600",
        text: "未知",
      },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.unknown;

    return (
      <span className={`px-2 py-1 text-xs ${config.className}`}>
        {config.text}
      </span>
    );
  };

  const handleViewDetail = (voiceItem: FigureItem) => {
    setSelectedVoice(voiceItem);
    setShowDetail(true);
  };

  const handleBackToList = () => {
    setShowDetail(false);
    setSelectedVoice(null);
  };

  const voiceColumns: ColumnDef<FigureItem>[] = [
    {
      header: '试听',
      accessorKey: 'demoUrl',
      cell: ({ row }) => <AudioPlayer demoUrl={row.original.demoUrl} />
    },
    {
      header: '声音名称',
      accessorKey: 'name',
    },
    {
      header: "类型",
      accessorKey: "type",
      cell: ({ row }) => <TypeBadge type={row.original.type} />,
    },
    {
      header: '用户手机号',
      accessorKey: 'userPhone',
      cell: ({ row }) => row.original.userPhone || '-',
    },
    {
      header: '交付时间',
      accessorKey: 'updatedAt',
      cell: ({ row }) => formatDate(new Date(row.original.updatedAt!).getTime()),
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="link" className="text-blue-500 p-0">
            复制ID
          </Button>
          <Button
            variant="link"
            className="text-blue-500 p-0"
            onClick={() => handleViewDetail(row.original)}
          >
            查看详情
          </Button>
        </div>
      ),
    },
  ];

  // 定制记录列定义
  const recordColumns: ColumnDef<FigureItem>[] = [
    {
      header: '声音名称',
      accessorKey: 'name',
    },
    {
      header: "类型",
      accessorKey: "type",
      cell: ({ row }) => <TypeBadge type={row.original.type} />,
    },
    {
      header: '用户手机号',
      accessorKey: 'userPhone',
      cell: ({ row }) => row.original.userPhone || '-',
    },
    {
      header: '提交时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => formatDate(new Date(row.original.createdAt!).getTime()),
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="link" className="text-blue-500 p-0">
            复制ID
          </Button>
          {
            row.original.status !== 'unconfirmed' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild className="border-none">
                  <Button variant="link" className="p-0 text-blue-500">
                    查看原因
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent side="bottom" className="flex items-center justify-center w-full">
                  <DropdownMenuItem>
                    {row.original.reason}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )
          }
        </div>
      ),
    },
  ];


  const renderDetailView = () => {
    if (!selectedVoice) return null;


    return (
      <div className="p-6 rounded-lg border">
        <div className="space-y-6 text-sm ">

          <div className="space-y-8">
            <div className="flex items-start">
              <p className="w-32 text-gray-500">声音名称:</p>
              <p className="flex-1 font-medium">{selectedVoice.name}</p>
            </div>

            <div className="flex items-start ">
              <p className="w-32 text-gray-500 ">用户手机号:</p>
              <p className="flex-1">{selectedVoice.userPhone || '-'}</p>
            </div>


            <div className="flex items-start">
              <p className="w-32 text-gray-500">训练音频:</p>
              <div className="flex-1 flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 p-0"
                  onClick={() => console.log('播放克隆声音')}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5V19L19 12L8 5Z" fill="currentColor" />
                  </svg>
                </Button>
                <Button variant="link" className="p-0 text-blue-500">
                  下载
                </Button>
              </div>
            </div>

            <div className="flex items-start">
              <p className="w-32 text-gray-500">克隆声音试听:</p>
              <div className="flex-1 flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8 p-0"
                  onClick={() => console.log('播放克隆声音')}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5V19L19 12L8 5Z" fill="currentColor" />
                  </svg>
                </Button>
                <Button variant="link" className="p-0 text-blue-500">
                  下载
                </Button>
              </div>
            </div>


            <div className="flex items-start">
              <p className="w-32 text-gray-500">创建时间:</p>
              <p className="flex-1">
                {formatDate(new Date(selectedVoice.createdAt!).getTime())}
              </p>
            </div>


            <div className="flex items-start">
              <p className="w-32 text-gray-500">交付时间:</p>
              <p className="flex-1">
                {formatDate(new Date(selectedVoice.updatedAt!).getTime())}
              </p>
            </div>


            {/* <div className="flex items-start">
              <p className="w-32 text-gray-500">审核用户备注:</p>
              <p className="flex-1 text-gray-400">
                {selectedVoice.remark || '--'}
              </p>
            </div> */}
          </div>
        </div>
      </div>
    );
  };

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="flex flex-col gap-4 p-4">
        {/* 面包屑导航 */}
        {showDetail ? (
          <div className="flex items-center text-sm  mb-2">
            <span
              className="text-gray-500 cursor-pointer hover:underline"
              onClick={() => {
                handleBackToList();
                resetSearchParams();
              }}
            >
              定制声音
            </span>
            <span className="mx-2">/</span>
            <span>声音详情</span>
          </div>
        ) : listType === 2 && (
          <div className="flex items-center text-sm mb-2">
            <span
              className="text-gray-500 cursor-pointer hover:underline"
              onClick={() => switchToListType(1)}
            >
              定制声音
            </span>
            <span className="mx-2">/</span>
            <span>定制记录</span>
          </div>
        )}

        <h1 className="text-xl font-bold">
          {showDetail ? '基础信息' : (listType === 1 ? '声音列表' : '定制列表')}
        </h1>


        {!showDetail &&
          <SearchBar
            onSearch={(params) => {
              setSearchParams({
                ...searchParams,
                searchType: params.searchType,
                searchText: params.searchText,
                type: params.type,
                channel: params.channel,
                ...(params.dateRange?.from && {
                  startDate: params.dateRange.from.getTime()
                }),
                ...(params.dateRange?.to && {
                  endDate: params.dateRange.to.getTime()
                }),
              });
              queryClient.invalidateQueries({ queryKey: ['fetchVoice'] });
            }}
            onReset={() => {
              setSearchParams({
                searchType: '1',
                searchText: '',
                startDate: undefined,
                endDate: undefined,
                type: '',
                channel: '',
              });
              queryClient.invalidateQueries({ queryKey: ['fetchVoice'] });
            }}
          />
        }


        {!showDetail && listType === 1 && (
          <Button
            className="w-[110px] text-xs"
            onClick={() => switchToListType(2)}
          >
            查看定制记录
          </Button>
        )}



        {showDetail ? (
          renderDetailView()
        ) : (
          <DataTable
            columns={listType === 1 ? voiceColumns : recordColumns}
            data={voice?.data || []}
            rowCount={voice?.totalSize}
            pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
            }}
            setPagination={setPagination}
          />
        )}
      </div>
    </ScrollArea>
  );
}