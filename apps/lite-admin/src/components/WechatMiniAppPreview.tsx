import CustomizedAppTop from '@/assets/szr/customized_app_top.png';
import NormalAppTop from '@/assets/szr/normal_app_top.png';
import AppBg1 from '@/assets/szr/app_bg_1.jpg';
import AppBg2 from '@/assets/szr/app_bg_2.jpg';
import { Button } from '@mono/ui/button';
import { WechatMiniAppTabs } from './WechatMiniAppTabs';

type PreviewType = 'customizedAI' | 'soundClone';

type CustomizedData = {
  videoUrl?: string;
  imgUrl?: string;
};

type SoundItem = {
  typeName: string;
  content: string;
  voiceUrl?: string;
  voiceSort?: number;
  _id: string;
};

type SoundData = SoundItem[];

/** 组件属性定义 */
interface WechatMiniAppPreviewProps {
  previewType: PreviewType;
  customizedData?: CustomizedData;
  soundData?: SoundData;
  filteredData?:SoundData;
}
/** 声音克隆组件属性定于 */
interface SoundCpnProps {
  soundData?: SoundData;
}


/**声音克隆预览组件 */
function SoundPreview({ soundData }: SoundCpnProps) {
  const safeSoundData = soundData ?? [];
  const backgroundStyle: React.CSSProperties = {
    backgroundImage: `url(${AppBg2})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  };


  return (
    <div style={backgroundStyle} className='h-full flex flex-col align-middle rounded-[12px] bg-slate-100'>
      <img src={NormalAppTop} alt="" />
      <div className='w-[335px] m-[20px]'>
        <WechatMiniAppTabs soundData={safeSoundData}/>
      </div>
    </div>
  );

}

/** 定制数字人预览组件 */
function CustomizedPreview({
  videoUrl,
  imgUrl
}: CustomizedData) {

  const backgroundStyle: React.CSSProperties = {
    backgroundImage: `url(${AppBg1})`,
    backgroundSize: 'cover',
    backgroundPosition: '-1260px -486px',
    backgroundRepeat: 'no-repeat',
  };

  return (
    <div style={backgroundStyle} className='h-full flex flex-col align-middle rounded-[12px] bg-slate-100'>
      <img src={CustomizedAppTop} alt="" />
      <video preload="auto" className='w-[335px] box-border m-[20px] rounded-[10px] h-[188px] bg-slate-400 object-cover' autoPlay={false}>
        <source src={videoUrl} type="video/mp4" />
      </video>
      <div className='w-[335px] m-[20px]'>
        <img className='w-full h-auto' src={imgUrl} />
      </div>
      <Button className='w-[335px] m-[20px] h-[56px]'>上传训练视频</Button>
    </div>
  );
}


/** 定制数字人预览组件 */
export function WechatMiniAppPreview({
  previewType,
  customizedData,
  soundData,
  
}: WechatMiniAppPreviewProps) {


  return (
    <div className='w-[375px] h-[812px]'>
      <div></div>
      {
        previewType === "customizedAI" ?
          <CustomizedPreview
            videoUrl={customizedData?.videoUrl}
            imgUrl={customizedData?.imgUrl}
          /> : <SoundPreview soundData={soundData} />
      }
    </div>
  );

}