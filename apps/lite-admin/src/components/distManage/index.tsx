import { <PERSON><PERSON><PERSON><PERSON> } from "@mono/ui/scroll-area";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
import { DataTable } from "../dataTable";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { addDistManage, allocateDistManage, fetDistManageList, updateDistManage } from "@/api/distManage";
import { Button } from "@mono/ui/button";
import { DistManageResponse } from "@/types/distManage";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTrigger } from "@mono/ui/dialog";
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Input } from "@mono/ui/input";
import { Label } from "@mono/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@mono/ui/select";
import { Loader2 } from "lucide-react";

export function DistManageIndex() {
  const queryClient = useQueryClient();
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [formState, setFormState] = useState({
    searchType: "1", 
    searchText: "",
    status: "1" // 1=全部, 2=启用, 3=禁用
  });

  const [searchParams, setSearchParams] = useState({
    searchType: "1",
    searchText: "",
    status: "1"
  });

  type DialogType = 'add' | 'edit' | 'allocate';
  const [open, setOpen] = useState(false);
  const [dialogType, setDialogType] = useState<DialogType>('add');
  const [currentDist, setCurrentDist] = useState<DistManageResponse | null>(null);
  const [nameOfAgent, setNameOfAgent] = useState('');
  const [userComment, setUserComment] = useState('');
  const [allocatedHashrate, setAllocatedHashrate] = useState('');

  const { data: distManageList } = useQuery({
    queryKey: ['fetDistManageList', pagination, searchParams],
    queryFn: () => fetDistManageList({
      page: pagination.pageIndex + 1,
      size: pagination.pageSize,
      searchType: searchParams.searchType,
      searchText: searchParams.searchText,
      status: searchParams.status !== "1" ? searchParams.status : undefined
    }),
  });

  const handleSearch = () => {
    setSearchParams({
      searchType: formState.searchType,
      searchText: formState.searchText,
      status: formState.status
    });
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const handleReset = () => {
    setFormState({
      searchType: "1",
      searchText: "",
      status: "1"
    });
    setSearchParams({
      searchType: "1",
      searchText: "",
      status: "1"
    });
    setPagination({ ...pagination, pageIndex: 0 });
  };

  const columns: ColumnDef<DistManageResponse>[] = [
    {
      header: '代理商昵称',
      cell: ({ row }) => <span>{row.original.appName}</span>,
    },
    {
      header: '剩余算力/总算力',
      cell: ({ row }) => <span>{row.original.hashrate}</span>,
    },
    {
      header: '子账号',
      cell: () => <span>暂无字段</span>,
    },
    {
      header: '状态',
      cell: ({ row }) => (
        <div className="flex items-center">
          <span
            className={`mr-2 scale-[3.3] text-${row.original.status === 1 ? 'green' : 'red'}-500`}
          >
            ·
          </span>
          <span>{row.original.status === 1 ? '启用' : '禁用'}</span>
        </div>
      )
    },
    {
      header: '添加时间',
      cell: ({ row }) => <span>{row.original.createdAt}</span>,
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex items-center gap-3 text-blue-500 cursor-pointer">
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <span
                className="hover:underline"
                onClick={() => {
                  setDialogType('edit');
                  setCurrentDist(row.original);
                  setNameOfAgent(row.original.appName);
                  setUserComment(row.original.description || '');
                }}
              >
                编辑
              </span>
            </DialogTrigger>
          </Dialog>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
              <span
                className="hover:underline"
                onClick={() => {
                  setDialogType('allocate');
                  setCurrentDist(row.original);
                  setAllocatedHashrate(''); 
                }}
              >
                分配算力
              </span>
            </DialogTrigger>
          </Dialog>
          <span
            className="hover:underline cursor-pointer"
            onClick={() => handleToggleStatus(row.original)}
          >
            {row.original.status === 1 ? '禁用' : '启用'}
          </span>
        </div>
      )
    },
  ];

  const addMutation = useMutation({
    mutationFn: addDistManage,
    onSuccess: () => {
      resetDialog();
      toast.success('代理商添加成功！');
      queryClient.invalidateQueries({ queryKey: ['fetDistManageList'] });
    },
    onError: (error) => {
      toast.error('添加代理商失败');
      console.error('添加代理商失败:', error);
    }
  });

  const updateMutation = useMutation({
    mutationFn: updateDistManage,
    onSuccess: () => {
      resetDialog();
      toast.success('代理商更新成功！');
      queryClient.invalidateQueries({ queryKey: ['fetDistManageList'] });
    },
    onError: (error) => {
      toast.error('更新代理商失败');
      console.error('更新代理商失败:', error);
    }
  });

  const allocateMutation = useMutation({
    mutationFn: allocateDistManage,
    onSuccess: () => {
      resetDialog();
      toast.success('算力分配成功！');
      queryClient.invalidateQueries({ queryKey: ['fetDistManageList'] });
    },
    onError: (error) => {
      toast.error('分配算力失败！');
      console.error('分配算力失败:', error);
    }
  });

  const resetDialog = () => {
    setNameOfAgent('');
    setUserComment('');
    setAllocatedHashrate('');
    setCurrentDist(null);
    setOpen(false);
  };

  const openAddDialog = () => {
    setDialogType('add');
    setNameOfAgent('');
    setUserComment('');
    setOpen(true);
  };

  const submitAction = () => {
    if (dialogType === 'add') {
      if (!nameOfAgent.trim()) {
        toast.warning('请输入代理商名称');
        return;
      }
      addMutation.mutate({
        appName: nameOfAgent,
        description: userComment
      });
    } else if (dialogType === 'edit' && currentDist) {
      if (!nameOfAgent.trim()) {
        toast.warning('请输入代理商名称');
        return;
      }
      updateMutation.mutate({
        id: currentDist._id,
        appName: nameOfAgent,
        description: userComment,
        status: currentDist.status
      });
    } else if (dialogType === 'allocate' && currentDist) {
      if (!allocatedHashrate.trim()) {
        toast.warning('请输入分配算力值');
        return;
      }
      allocateMutation.mutate({
        id: currentDist._id,
        hashrate: allocatedHashrate
      });
    }
  };

  const handleToggleStatus = (rowData: DistManageResponse) => {
    const newStatus = rowData.status === 1 ? 0 : 1;
    updateMutation.mutate({
      id: rowData._id,
      appName: rowData.appName,
      description: rowData.description,
      status: newStatus
    });
    queryClient.invalidateQueries({ queryKey: ['fetDistManageList'] });
  };



  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="flex flex-col p-8 w-full h-full gap-5">
        <div className="font-semibold text-[18px]">代理商管理</div>
        <>
          <div className="p-4 rounded-lg shadow">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">条件搜索</label>
                <Select
                  value={formState.searchType}
                  onValueChange={(value) => setFormState({ ...formState, searchType: value })}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择搜索类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">代理商名称</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Input
                placeholder='输入搜索内容'
                className="w-48"
                value={formState.searchText}
                onChange={(e) => setFormState({ ...formState, searchText: e.target.value })}
              />

              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">状态</label>
                <Select
                  value={formState.status}
                  onValueChange={(value) => setFormState({ ...formState, status: value })}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="选择搜索状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">全部</SelectItem>
                    <SelectItem value="2">启用</SelectItem>
                    <SelectItem value="3">禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={handleSearch}>搜索</Button>
              <Button variant="outline" onClick={handleReset}>重置</Button>
            </div>
          </div>
        </>
        <div className="gap-2 flex">
          <Button onClick={openAddDialog}>添加代理商</Button>
        </div>
        <DataTable
          columns={columns}
          data={distManageList?.data}
          pagination={pagination}
          setPagination={setPagination}
          rowCount={distManageList?.totalSize}
        />

        {/* 代理商弹窗 */}
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogContent className="w-full max-w-xl">
            {dialogType === 'allocate' ? (
              <>
                <DialogHeader className="w-full pb-4 items-center border-b justify-center text-center">
                  分配算力
                </DialogHeader>
                <div className="flex-col gap-4 flex">
                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <Label className="text-[14px] w-32 flex justify-end">
                      主账号算力余额
                    </Label>
                    <div className="w-[340px] ml-4 text-sm">
                      {/* 暂无主账号算力余额 */}
                      <span>10000</span>
                    </div>
                  </div>

                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <Label className="text-[14px] w-32 flex justify-end">
                      代理商名称
                    </Label>
                    <div className="w-[340px] ml-4">
                      <span>{currentDist?.appName}</span>
                    </div>
                  </div>

                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <Label className="text-[14px] w-32 flex justify-end">
                      代理商算力余额
                    </Label>
                    <div className="w-[340px] ml-4">
                      <span>{currentDist?.hashrate}</span>
                    </div>
                  </div>

                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <div className="w-32 flex ">
                      <div className="flex justify-end w-32">
                        <img src={RequiredIco} className="w-[14px] h-[14px]" />
                      </div>
                      <Label className="text-[14px] w-32 flex justify-end">
                        分配算力
                      </Label></div>
                    <div className="w-[340px] ml-4">
                      <Input
                        value={allocatedHashrate}
                        onChange={(e) => setAllocatedHashrate(e.target.value)}
                        className="w-full"
                        placeholder="请输入分配算力值"
                      />
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <>
                <DialogHeader className="w-full pb-4 items-center border-b justify-center text-center">
                  {dialogType === 'add' ? '添加代理商' : '编辑代理商'}
                </DialogHeader>
                <div className="flex-col">
                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <img src={RequiredIco} className="w-[14px] h-[14px]" />
                    <Label className="text-[14px] w-20">
                      代理商名称
                    </Label>
                    <div className="w-[340px] ml-4 mt-[-6px] flex-col">
                      <Input
                        value={nameOfAgent}
                        onChange={(e) => setNameOfAgent(e.target.value)}
                        className="w-full"
                        placeholder="请输入代理商名称"
                        maxLength={20}
                      />
                      <small className="text-[12px] text-gray-500">最多支持输入20个字符</small>
                    </div>
                  </div>

                  <div className="flex text-left justify-start mr-4 pr-4 h-fit-content mt-2">
                    <Label className="text-[14px] w-20 flex justify-end">
                      用户备注
                    </Label>
                    <div className="w-[340px] ml-7 mt-[-6px] flex-col">
                      <Input
                        value={userComment}
                        onChange={(e) => setUserComment(e.target.value)}
                        className="w-full"
                        placeholder="请输入备注"
                        maxLength={50}
                      />
                      <small className="text-[12px] text-gray-500">最多支持输入50个字符</small>
                    </div>
                  </div>
                </div>
              </>
            )}

            <DialogFooter className="pr-4 gap-1">
              <Button variant='ghost' className="border" onClick={resetDialog}>取消</Button>
              <Button
                onClick={submitAction}
                disabled={addMutation.isPending || updateMutation.isPending || allocateMutation.isPending}
                className="hover:bg-primary/90"
              >
                {(addMutation.isPending || updateMutation.isPending || allocateMutation.isPending) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                确认
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </ScrollArea>
  );
}