import { useState } from 'react';
import { Button } from "@mono/ui/button";
import { CustomizedTypeMap, CustomizedTypeEnum } from "@/types/customizedAICharacter";

interface RadioButtonProps {
  activeType: string;
  onChange:(activeType: CustomizedTypeEnum) => void;
}
export function RadioButton({
  activeType,
  onChange = ()=> {}
}: RadioButtonProps) {

  const [customizedType, setCustomizedType] = useState(activeType);
  return (
    <div className="flex gap-8 pt-4">
      <Button 
        onClick={()=> {
          setCustomizedType(CustomizedTypeEnum.FAST);
          onChange(CustomizedTypeEnum.FAST);
        }}
        className={`rounded-full w-20  radio_inactive_btn ${customizedType === CustomizedTypeEnum.FAST ? 'radio_active_btn':''}`}>{CustomizedTypeMap.PRO}</Button>
      <Button 
        onClick={()=> {
          setCustomizedType(CustomizedTypeEnum.PRO);
          onChange(CustomizedTypeEnum.PRO);
        }}
        className={`rounded-full w-20 radio_inactive_btn ${customizedType === CustomizedTypeEnum.PRO ? 'radio_active_btn':''}`}>{CustomizedTypeMap.FAST}</Button>

      <style>{`
          .radio_inactive_btn {
            background-color: #f3e8ff;
            color: #18181b;
          }
          .radio_inactive_btn:hover{
            background-color: #f3e8ff;
            color: #18181b;
          }
          .radio_active_btn {
						background-color: #4c1d95;
            color: white;
					}
          .radio_active_btn:hover {
            background-color: #4c1d95;
            color: white;
          }
          
				`}</style>
    </div>
  );
}