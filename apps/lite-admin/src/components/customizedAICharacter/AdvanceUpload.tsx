import { useState, useRef, useEffect } from "react";
import { Plus, X, Play, Volume2, Image } from "lucide-react";
import { Button } from "@mono/ui/button";
import { Input } from "@mono/ui/input";


type UploadedFile = {
  url: string;
  type: "image" | "video" | "audio";
  name: string;
  size?: number;
};

interface MediaUploadProps {
  fileType: string;
  onChange: (file?: File) => void;
  initialUrl?: string;
  onDelete?: (isDeleted: boolean) => void;
  style?: React.CSSProperties;
}

export function MediaUpload({
  fileType = "image",
  onChange,
  initialUrl,
  onDelete,
  style,
}: MediaUploadProps) {
  const [file, setFile] = useState<UploadedFile | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const FILE_SIZE_LIMITS = {
    image: 2 * 1024 * 1024,
    video: 100 * 1024 * 1024,
    audio: 10 * 1024 * 1024
  };
  useEffect(() => {
    if (initialUrl) {
      const cleanedUrl =
        fileType === "video" ? initialUrl.split("?")[0] : initialUrl;

      setFile({
        url: cleanedUrl,
        type: fileType as "image" | "video" | "audio",
        name: cleanedUrl.split("/").pop() ?? "unknown",
      });
    }

  }, [initialUrl, fileType]);


  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;
    const fileType = selectedFile.type.split("/")[0];
    if (fileType !== "image" && fileType !== "video" && fileType !== "audio") {
      alert("仅支持图片、视频和音频文件");
      return;
    }

    // 校验文件
    const fileTypeCategory = selectedFile.type.split("/")[0];
    if (fileTypeCategory !== "image" && fileTypeCategory !== "video" && fileTypeCategory !== "audio") {
      alert("仅支持图片、视频和音频文件");
      return;
    }

    // 校验文件大小
    const sizeLimit = FILE_SIZE_LIMITS[fileTypeCategory as keyof typeof FILE_SIZE_LIMITS];
    if (selectedFile.size > sizeLimit) {
      alert(`文件大小超过限制，${fileTypeCategory === "image" ? "图片" : "视频"}文件应小于${formatFileSize(sizeLimit)}`);
      if (inputRef.current) inputRef.current.value = "";
      return;
    }

    const fileUrl = URL.createObjectURL(selectedFile);
    setFile({
      url: fileUrl,
      type: fileTypeCategory as "image" | "video" | "audio",
      name: selectedFile.name,
      size: selectedFile.size
    });
    onChange(selectedFile);
  };

  // 格式
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (file) {
      URL.revokeObjectURL(file.url);
      setFile(null);
    }
    if (inputRef.current) {
      inputRef.current.value = "";
    }
    onChange(undefined);
    if (onDelete) {
      onDelete(true);
    }
  };

  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (file) {
      window.open(file.url, "_blank");
    }
  };

  const triggerFileInput = () => {
    inputRef.current?.click();
  };

  const [showDefaultMedia, setShowDefaultMedia] = useState(false);
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowDefaultMedia(true);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      className="relative w-[80px] h-[80px] rounded-md border border-dashed border-gray-300 flex items-center justify-center cursor-pointer overflow-hidden bg-gray-50 hover:bg-gray-100 transition-colors"
      onClick={triggerFileInput}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={style}
    >
      <Input
        type="file"
        ref={inputRef}
        onChange={handleFileChange}
        accept={fileType + "/*"}
        className="hidden"
      />

      {!file ? (
        showDefaultMedia && (
          fileType === "image" ? (
            <div className="relative w-full h-full animate-fade-in bg-white-100" style={{ animationDuration: '300ms' }}>
              <div className="absolute inset-0 bg-gray/10 flex items-center justify-center">
                <Plus className="w-5 h-5 text-gray-400" />
              </div>
            </div>

          ) : fileType === "video" ? (
            <div className="relative w-full h-full animate-fade-in" style={{ animationDuration: '300ms' }}>
              <video className="w-full h-full object-cover" src="https://placeholder.im/30x30.png/f5f7fa">
                <source
                  src=""
                  type="video/mp4"
                />
              </video>
              <div className="absolute inset-0 bg-gray/10 flex items-center justify-center">
                <Plus className="w-5 h-5 text-gray-400" />
              </div>
            </div>
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center animate-fade-in" style={{ animationDuration: '300ms' }}>
              <Volume2 className="w-6 h-6 text-gray-500" />
            </div>
          ))
      ) : (
        <>
          {file.type === "image" ? (
            <img
              key={file.url}
              src={file.url || 'https://do-while.oss-cn-beijing.aliyuncs.com/file/a4107e4116fac3dc65d559c8745292f2.png'}
              alt={file.name}
              className="w-full h-full object-cover"
            />
          ) : file.type === "video" ? (
            <div className="relative w-full h-full">
              <video className="w-full h-full object-cover" src={file.url || 'https://do-while.oss-cn-beijing.aliyuncs.com/file/10af354ef38ff867e4f93c36c5b0bfd5.mp4'}>
                <source
                  src={file.url || 'https://do-while.oss-cn-beijing.aliyuncs.com/file/10af354ef38ff867e4f93c36c5b0bfd5.mp4'}
                  type="video/mp4" />
              </video>
            </div>
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <Volume2 className="w-6 h-6 text-gray-500" />
            </div>
          )}

          {isHovered && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="w-6 h-6 rounded-full "
                onClick={handlePreview}
              >
                {file.type === "image" ? (
                  <Image className="w-3 h-3 " />
                ) : (
                  <Play className="w-3 h-3 " />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="w-6 h-6 rounded-full "
                onClick={handleDelete}
              >
                <X className="w-3 h-3 " />
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}