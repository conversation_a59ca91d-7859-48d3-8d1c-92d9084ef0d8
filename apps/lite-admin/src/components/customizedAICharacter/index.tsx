import { useState, useEffect, useMemo } from 'react';
import { ScrollArea } from '@mono/ui/scroll-area';
import { RadioButton } from './radioButton';
import RequiredIco from '@/assets/svg/required_ico.svg';
import { Textarea } from '@mono/ui/textarea';
import { Alert } from '@mono/ui/alert';
import { Button } from "@mono/ui/button";
import OSS from 'ali-oss';
import { makeRequest } from '../../api';
import { CustomizedTypeEnum } from '@/types/customizedAICharacter';
import { fetchCustomizedAIConfig } from '@/api/customizedAICharacter';
import { MediaUpload } from "./AdvanceUpload";
import { getStsCredentials } from '@/api/desktop';
import { WechatMiniAppPreview } from '../WechatMiniAppPreview';
import { useQuery } from '@tanstack/react-query';
import { Progress } from "@mono/ui/progress";
import { useNavigate } from '@tanstack/react-router';
import { Check } from "lucide-react";
import { LoadingContainer } from '../loading';

const defaultVideoUrl = 'https://do-while.oss-cn-beijing.aliyuncs.com/file/10af354ef38ff867e4f93c36c5b0bfd5.mp4';
const defaultImageUrl = 'https://do-while.oss-cn-beijing.aliyuncs.com/file/a4107e4116fac3dc65d559c8745292f2.png';
const occupyImageUrl = 'https://placeholder.im/300x200.png/f1f5f9';
const defaultCourseText = '大家看到的我其实不是真的我，这是我的数字人分身，你看他的形象、动作甚至口型都和我一模一样。他能代替我出镜，帮我做视频，从文案到剪辑，都能一键生成，甚至还能讲一口流利的外语。Just upload one video,And you can also generate your own AI digi';

const DEFAULT_FILE_MARKER = '__DEFAULT_FILE__';
//转文件
const createDefaultFile = (url: string, type: string): File => {
  console.log(url);
  const blob = new Blob([], { type });
  const file = new File([blob], DEFAULT_FILE_MARKER, { type });
  Object.defineProperty(file, 'isDefault', { value: true });
  return file;
};


async function setDesktopConfig(input: {
  type: string;
  videoFile?: File | string | null;
  imgFile?: File | string | null;
  courseText?: string | null;
  onVideoUploadProgress?: (percent: number) => void;
}) {
  //OSS-89
  let proCourseVideoUrl = '';
  let proCourseImageUrl = '';
  let topspeedCourseVideoUrl = '';
  let topspeedCourseImageUrl = '';
  const credentials = await getStsCredentials();
  const client = new OSS({
    region: credentials.region,
    accessKeyId: credentials.accessKeyId,
    accessKeySecret: credentials.accessKeySecret,
    stsToken: credentials.securityToken,
    bucket: credentials.bucket,
    secure: true,
    refreshSTSToken: async () => {
      const creds = await getStsCredentials();
      return {
        accessKeyId: creds.accessKeyId,
        accessKeySecret: creds.accessKeySecret,
        stsToken: creds.securityToken,
      };
    },
    refreshSTSTokenInterval: 300000, // 5分钟刷新间隔
  });

  const env = import.meta.env.VITE_APP_NODE_ENV;

  // 视频处理
  if (input.videoFile !== null) {
    if (typeof input.videoFile === 'string') {
      if (input.type === CustomizedTypeEnum.PRO) {
        proCourseVideoUrl = input.videoFile;
      } else {
        topspeedCourseVideoUrl = input.videoFile
      }
    } else if (input.videoFile) {
      const videoPath = `${env}/cus_ai_chara/100001/video/${input.videoFile.name}`;
      const videoUploadResult = await client.multipartUpload(videoPath, input.videoFile, {
        progress: (p) => {
          if (input.onVideoUploadProgress) {
            input.onVideoUploadProgress(p);
          }
        },
      });
      const url = client.signatureUrl(videoUploadResult.name);

      if (input.type === CustomizedTypeEnum.PRO) {
        proCourseVideoUrl = url;
      } else {
        topspeedCourseVideoUrl = url;
      }
    }
  }

  // 图片处理
  if (typeof input.imgFile === 'string') {
    if (input.type === CustomizedTypeEnum.PRO) {
      proCourseImageUrl = input.imgFile;
    } else {
      topspeedCourseImageUrl = input.imgFile;
    }
  } else if (input.imgFile) {
    const imgPath = `${env}/cus_ai_chara/100001/img/${input.imgFile.name}`;
    const uploadRes = await client.put(imgPath, input.imgFile);
    const url = uploadRes?.url || '';
    if (input.type === CustomizedTypeEnum.PRO) {
      proCourseImageUrl = url;
    } else {
      topspeedCourseImageUrl = url;
    }
  }

  return makeRequest<object>({
    url: '/settings/save',
    method: 'POST',
    data: {
      proCourseVideoUrl: proCourseVideoUrl || undefined,
      proCourseImageUrl: proCourseImageUrl || undefined,
      topspeedCourseVideoUrl: topspeedCourseVideoUrl || undefined,
      topspeedCourseImageUrl: topspeedCourseImageUrl || undefined,
      proCourseText: input.courseText ?? undefined,
    },
  });
}

export function CustomizedAICharacterPage() {
  const [customizedType, setCustomizedType] = useState(CustomizedTypeEnum.PRO);

  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  // 预览URL
  const [videoPreviewUrl, setVideoPreviewUrl] = useState<string | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);
  const [courseText, setCourseText] = useState<string>('');

  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isUploading, setIsUploading] = useState<boolean>(false);

  const [videoPriori, setvideoPriori] = useState<string | null>(null);
  const [imagePriori, setimagePriori] = useState<string | null>(null);

  const [isVideoDeleted, setIsVideoDeleted] = useState(false);
  const [isImageDeleted, setIsImageDeleted] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [originalText, setOriginalText] = useState<string>('');

  const { data, refetch, isLoading } = useQuery({
    queryKey: ['customizedAIConfig'],
    queryFn: fetchCustomizedAIConfig,
    staleTime: 60 * 1000,
  });


  useEffect(() => {
    if (!data?.proCourseText) { setCourseText(defaultCourseText) }
  }, [data?.proCourseText])
  const handleVideoUpload = (file?: File) => {
    if (file) {
      if (!file.type.startsWith('video/mp4')) {
        alert('视频格式不支持，请上传mp4格式的视频');
        return;
      }
      if (file.size > 100 * 1024 * 1024) { // 100MB
        alert('视频文件大小超过限制，请上传小于100MB的视频');
        return;
      }
      setVideoFile(file);
      const url = URL.createObjectURL(file);
      setVideoPreviewUrl(url);
      setIsVideoDeleted(false);
    }
  };

  const handleImageUpload = (file?: File) => {
    if (file) {
      if (!['image/jpeg', 'image/png'].includes(file.type)) {
        alert('图片格式不支持，请上传jpg或png格式的图片');
        return;
      }
      if (file.size > 2 * 1024 * 1024) { // 2MB
        alert('图片文件大小超过限制，请上传小于2MB的图片');
        return;
      }
      setImageFile(file);
      const url = URL.createObjectURL(file);
      setImagePreviewUrl(url);
      setIsImageDeleted(false);
    }
  };
  const hasTextChanged = useMemo(() => {
    return courseText.trim() !== originalText.trim() && customizedType !== 'PRO' && courseText?.trim();
  }, [courseText, originalText, customizedType]);
  // 上传
  const handleSave = async () => {
    const isDefaultVideo = videoFile?.name === DEFAULT_FILE_MARKER;
    const isDefaultImage = imageFile?.name === DEFAULT_FILE_MARKER;

    const hasVideoChanged = videoFile && !isVideoDeleted &&
      (isDefaultVideo || videoFile.name !== data?.proCourseVideoUrl?.split('/').pop());
    const hasImageChanged = imageFile && !isImageDeleted &&
      (isDefaultImage || imageFile.name !== data?.proCourseImageUrl?.split('/').pop());
    console.log('当前文件是', videoFile?.name);

    if (!hasVideoChanged && !hasImageChanged && !hasTextChanged) {
      console.log("数据未变化");
      return;
    }

    if (hasVideoChanged) {
      setIsUploading(true);
      setUploadProgress(0);
    }

    try {
      const payload: Parameters<typeof setDesktopConfig>[0] = {
        type: customizedType,
        videoFile: hasVideoChanged
          ? (isDefaultVideo ? defaultVideoUrl : videoFile!)
          : null,
        imgFile: hasImageChanged
          ? (isDefaultImage ? defaultImageUrl : imageFile!)
          : null,
        courseText: hasTextChanged ? courseText : null,
        onVideoUploadProgress: hasVideoChanged
          ? (p) => {
            setUploadProgress(parseFloat((p * 100).toFixed(2)));
          }
          : undefined,
      };

      const result = await setDesktopConfig(payload);
      console.log("保存成功", result);
      await refetch()
      if (hasTextChanged) {
        setOriginalText(courseText);
      }

      setShowAlert(true);
      setTimeout(() => {
        setShowAlert(false);
      }, 3000);

      if (hasVideoChanged) {
        setVideoFile(null);
        setVideoPreviewUrl(null);
      }
      if (hasImageChanged) {
        setImageFile(null);
        setImagePreviewUrl(null);
      }


      if (hasTextChanged) setCourseText(courseText);
    } catch (error) {
      console.error("保存失败", error);
    } finally {
      setIsUploading(false);
    }
  };


  const navigate = useNavigate();
  const handleButtonClick = () => {
    navigate({ to: '/fastClone' });
  };
  //useMemo缓存预览 URL
  const previewVideoUrl = useMemo(() => {
    if (isVideoDeleted) return '';
    return videoPreviewUrl ??
      (customizedType === CustomizedTypeEnum.PRO
        ? (data?.proCourseVideoUrl?.split('?')[0] || '')
        : (data?.topspeedCourseVideoUrl?.split('?')[0] || ''));
  }, [videoPreviewUrl, data, customizedType, isVideoDeleted]);

  const previewImageUrl = useMemo(() => {
    if (isImageDeleted) return '';
    return imagePreviewUrl ??
      (customizedType === CustomizedTypeEnum.PRO
        ? (data?.proCourseImageUrl || occupyImageUrl)
        : (data?.topspeedCourseImageUrl || occupyImageUrl));
  }, [imagePreviewUrl, data, customizedType, isImageDeleted]);


  useEffect(() => {
    if (data) {
      const initialText = data.proCourseText || defaultCourseText;
      setCourseText(initialText);
      setOriginalText(initialText);
    }
  }, [data]);

  useEffect(() => {
    if (!data) return;
    setVideoFile(null);
    setImageFile(null);
    setVideoPreviewUrl(null);
    setImagePreviewUrl(null);
    setIsVideoDeleted(false);
    setIsImageDeleted(false);

    const proVid = data.proCourseVideoUrl?.split('?')[0] ?? '';
    const fastVid = data.topspeedCourseVideoUrl?.split('?')[0] ?? '';
    setvideoPriori(customizedType === CustomizedTypeEnum.PRO ? proVid : fastVid);

    const proImg = data.proCourseImageUrl ?? occupyImageUrl;
    const fastImg = data.topspeedCourseImageUrl ?? occupyImageUrl;
    setimagePriori(customizedType === CustomizedTypeEnum.PRO ? proImg : fastImg);
  }, [data, customizedType]);


  const [showPreview, setShowPreview] = useState(false);
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowPreview(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  const handleResetDefault = (type: 'video' | 'image') => {
    const defaultUrl = type === 'video' ? defaultVideoUrl : defaultImageUrl;
    const fileType = type === 'video' ? 'video/mp4' : 'image/png';

    if (type === 'video') {
      setVideoPreviewUrl(defaultUrl);
      setVideoFile(createDefaultFile(defaultUrl, fileType));
      setvideoPriori(defaultUrl);
      setIsVideoDeleted(false);
    } else {
      setImagePreviewUrl(defaultUrl);
      setImageFile(createDefaultFile(defaultUrl, fileType));
      setimagePriori(defaultUrl);
      setIsImageDeleted(false);
    }
  };

  if (isLoading) {
    return (
      <LoadingContainer />
    )
  }

  return (
    <ScrollArea className="h-full" enableHorizontal>
      <div className="pl-2 pt-4">
        <div className="text-2xl font-bold">定制数字人</div>
        {showAlert && (
          <div className="fixed inset-x-0 top-0 flex justify-center items-start mt-[20px] z-50">
            <Alert
              variant="default"
              className="
              flex flex-col items-center 
              justify-center  p-4 
              rounded-md shadow-xl w-[130px] 
              pl-1 pr-0 h-10 transition 
              duration-300"
            >
              <Check color="white" className="w-6 h-6 transform scale-[0.7]  bg-green-500 rounded-xl mt-[-8px]" />
              <span className="text-sm text-center">保存成功！</span>
            </Alert>
          </div>
        )}
        {/* 切换 */}
        <RadioButton
          activeType={customizedType}
          onChange={(type: CustomizedTypeEnum) => {
            setCustomizedType(type);
          }}
        />

        {/* 左侧布局 */}
        <div className='flex flex-row mt-8 space-x-8'>
          <div className='w-[640px]'>
            <h4 className='text-[14px] mt-4 mb-4 font-bold'>定制教程</h4>

            <div className='flex'>
              <div className='flex w-[160px] text-right justify-end mr-4 h-fit-content mt-2'>
                <img src={RequiredIco} alt="" className='w-[16px] h-[13px]' />
                <div className='text-[14px]'>教程视频</div>
              </div>
              <div className="flex flex-col space-y-4 mt-2">
                <MediaUpload
                  onChange={handleVideoUpload}
                  initialUrl={videoPriori || ''}
                  fileType="video"
                  onDelete={(deleted) => setIsVideoDeleted(deleted)}
                />
                {isVideoDeleted && (
                  <small className="text-xs text-red-500 mt-[0px] h-2 animate-pulse"
                    key={`${customizedType}-${previewVideoUrl}-${previewImageUrl}`}
                  >
                    <div className="mt-[-8px] mb-0">请上传教程视频</div>
                  </small>
                )}
                {isUploading && (
                  <div className="w-full mt-2">
                    <Progress value={uploadProgress} className="w-[80px] h-[3px] mb-2" />
                    <div className="text-xs mt-1 text-gray-600">{uploadProgress}%</div>
                  </div>
                )}
                <small className="text-xs text-gray-500 pb-0 ">
                  <div>用户进行数字人训练时，展示的教程视频</div>
                  <div>推荐尺寸1280*720（横屏）</div>
                  <div>支持格式:mp4，文件大小＜100MB</div>
                </small>
                <a
                  href='#'
                  onClick={(e) => {
                    e.preventDefault();
                    handleResetDefault('video');
                  }}
                  className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer pl-0 pb-2 w-14"
                >
                  恢复默认
                </a>
              </div>
            </div>

            <div className='flex'>
              <div className='flex w-[160px] text-right justify-end mr-4 h-fit-content mt-2'>
                <img src={RequiredIco} className='w-[16px] h-[13px]' />
                <div className='text-[14px] '>图文教程</div>
              </div>
              <div className="flex flex-col space-y-4 mt-2">
                <div>
                  <MediaUpload
                    onChange={handleImageUpload}
                    initialUrl={imagePriori || ''}
                    fileType='image'
                    onDelete={(deleted) => setIsImageDeleted(deleted)}
                  />
                  {isImageDeleted && (
                    <small className="text-xs text-red-500 mt-20 animate-pulse">
                      <div className='mt-[8.5px]'>请上传图文教程</div>
                    </small>
                  )}

                </div>
                <small className="text-xs text-gray-500">
                  <div>用户进行数字人训练时，展示的图文介绍</div>
                  <div>推荐尺寸750*750</div>
                  <div>支持格式：jpg, png，文件大小＜2MB</div>
                </small>
                <a
                  href='#'
                  onClick={(e) => {
                    e.preventDefault();
                    handleResetDefault('image');
                  }}
                  className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer pl-0 pb-2 w-14"
                >
                  恢复默认
                </a>
                {customizedType === CustomizedTypeEnum.PRO && (
                  <Button onClick={handleSave} className='w-[150px]'>保存</Button>
                )}
              </div>
            </div>
            {customizedType === CustomizedTypeEnum.FAST ? (
              <div className='w-[640px]'>
                <h4 className='text-[14px] mt-4 mb-4 font-bold'>生成样片</h4>
                <div>
                  <div className='flex'>
                    <div className='flex w-[160px] text-right justify-end mr-4 h-fit-content mt-2'>
                      <img src={RequiredIco} className='w-[16px]  h-[13px]' />
                      <div className='text-[14px] '>生成文案</div>
                    </div>
                    <div className="flex flex-col space-y-2 mt-2">
                      <Textarea
                        value={courseText || ''}
                        onChange={(e) => setCourseText(e.target.value)}
                        placeholder="请输入正文内容"
                        rows={4}
                        className="rounded-sm focus:border-gray-500 w-[350px] h-[150px] focus:!ring-0"
                      />
                      {!courseText?.trim() && (
                        <small className="text-xs text-red-500 mt-2 animate-pulse">
                          <div>请输入生成文案</div>
                        </small>
                      )}

                      <small className="text-xs text-gray-500">
                        用户训练数字人完成，生成样片的文案
                      </small>
                      <a
                        href='#'
                        onClick={(e) => {
                          e.preventDefault();
                          setCourseText(defaultCourseText);
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer pl-0 p-3 w-16"
                      >
                        恢复默认
                      </a>
                      <Button onClick={handleSave} className='w-[150px]'>保存</Button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className='w-[640px]'>
                <h4 className='text-[14px] mt-4 mb-4 font-bold'>模板状态</h4>
                <div className='flex pl-28'>
                  <div className='flex flex-col space-y-2 text-[14px]'>
                    <div className='flex text-right items-center justify-first mr-4 mt-2'>
                      <div>功能总开关：</div>
                      <div>开启</div>
                    </div>
                    <div className='flex text-right items-center justify-first mr-4 mt-2'>
                      <div>可见范围：</div>
                      <div>所有人可用</div>
                    </div>
                    <div className="flex flex-col space-y-2 mt-2">
                      <Button className='w-[150px] rounded-none' onClick={handleButtonClick}>
                        前往设置极速克隆
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className='flex-1'>
            {showPreview ? (
              <WechatMiniAppPreview
                key={`${customizedType}-${previewVideoUrl}-${previewImageUrl}`}
                previewType="customizedAI"
                customizedData={{
                  imgUrl: previewImageUrl,
                  videoUrl: previewVideoUrl
                }}
              />
            ) : (
              <div>
              </div>
            )}
          </div>
        </div>
      </div>



    </ScrollArea>
  );
}