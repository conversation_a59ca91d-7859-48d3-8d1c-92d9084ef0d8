import { Package2, ChevronRight } from 'lucide-react';
import { Link } from '@tanstack/react-router';
import { navs, NavItem } from './navs-datasource';
import { Mine } from './mine';
import { useState } from 'react';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarProvider,
} from '@mono/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@mono/ui/collapsible';
// import { useUserStore } from '@/store/user.ts';
// function RouterSpinner() {
//   const isLoading = useRouterState({ select: (s) => s.status === 'pending' });
//   return <Spinner show={isLoading} />;
// }

function NavMenuItem({ item }: { item: NavItem }) {
  const [isOpen, setIsOpen] = useState(false);

  if (item.children && item.children.length > 0) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton>
              {item.icon}
              <span>{item.name}</span>
              <ChevronRight className={`ml-auto h-4 w-4 transition-transform ${isOpen ? 'rotate-90' : ''}`} />
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.children.map((child) => (
                <SidebarMenuSubItem key={child.path}>
                  <SidebarMenuSubButton asChild>
                    <Link to={child.path!}>
                      {child.icon}
                      <span>{child.name}</span>
                    </Link>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              ))}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    );
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild>
        <Link to={item.path!}>
          {item.icon}
          <span>{item.name}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

export function AppSidebar() {
  // const isSuper = useUserStore((state) => state.isSuper);

  return (
    <Sidebar>
      <SidebarHeader>
        <Link className="flex items-center gap-2 font-semibold p-2" to={'/'}>
          <Package2 className="w-6 h-6" />
          <span className="truncate">后台管理系统</span>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {navs.map((item) => (
            <NavMenuItem key={item.path || item.name} item={item} />
          ))}
        </SidebarMenu>
      </SidebarContent>
      <div className="mt-auto p-4">
        <Mine />
      </div>
    </Sidebar>
  );
}
