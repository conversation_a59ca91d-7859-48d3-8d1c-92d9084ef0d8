import {
  ScrollText,
  Megaphone,
  UserRoundCog,
  Rocket,
  ClipboardList,
  Users,
  User,
  ChartNoAxesCombined,
  ShieldCheck,
  Share2,
  TrendingUp,
  Globe,
  Activity,
} from 'lucide-react';
import { ReactNode } from 'react';

// 导航菜单项类型定义
export interface NavItem {
  name: string;
  icon: ReactNode;
  path?: string;
  children?: NavItem[];
  isAdmin?: boolean;
}

// 导航数据
export const navs: NavItem[] = [
  {
    name: '用户列表',
    icon: <User className="w-5 h-5" />,
    path: '/clientUser',
  },
  {
    name: '团队列表',
    icon: <Users className="w-5 h-5" />,
    path: '/team',
  },
  {
    name: '媒体账号',
    icon: <Globe className="w-5 h-5" />,
    path: '/platformAccount',
  },
  {
    name: '应用管理',
    icon: <Globe className="w-5 h-5" />,
    children: [
      {
        name: '极速克隆',
        icon: <Globe className="w-5 h-5" />,
        path: '/fastClone',
      },
      {
        name: '定制数字人',
        icon: <Globe className="w-5 h-5" />,
        path: '/customizedAICharacter',
      },
      {
        name: '声音克隆',
        icon: <Share2 className="w-5 h-5" />,
        path: '/soundClone',
      },
      {
        name: '频道管理',
        icon: <Share2 className="w-5 h-5" />,
        path: '/channelManage',
      },
    ],
  },
  {
    name: '兑换码',
    icon: <Share2 className="w-5 h-5" />,
    path: '/exchangePackage',
  },
  {
    name: '代理商管理',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/distManage',
  },
  {
    name: '算力消耗',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/computeDemand',
  },
  {
    name: '团队订单',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/userOrder',
  },
  {
    name: '算力中心',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/computeCenter',
  },
  {
    name: '数字人',
    icon: <Share2 className="w-5 h-5" />,
    path: '/figure',
  },
  {
    name: '生成视频',
    icon: <Share2 className="w-5 h-5" />,
    path: '/generateVideo',
  },
  {
    name: '定制声音',
    icon: <Share2 className="w-5 h-5" />,
    path: '/voice',
  },

  {
    name: '订单管理',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/order',
  },
  // {
  //   name: '业绩分析',
  //   icon: <TrendingUp className="w-5 h-5" />,
  //   path: '/performance-analysis',
  // },
  {
    name: '数据',
    icon: <ChartNoAxesCombined className="w-5 h-5" />,
    path: '/data',
  },
  // {
  //   name: '登陆保持统计',
  //   icon: <Activity className="h-5 w-5" />,
  //   path: '/online-statistics',
  // },
  {
    name: '渠道管理',
    icon: <Share2 className="w-5 h-5" />,
    path: '/channel',
  },
  // {
  //   name: '广告管理',
  //   icon: <Megaphone className="w-5 h-5" />,
  //   path: '/ad',
  // },
  // {
  //   name: '系统通知',
  //   icon: <Megaphone className="w-5 h-5" />,
  //   path: '/systemNotify',
  // },
  // {
  //   name: '云端数据白名单',
  //   icon: <ShieldCheck className="w-5 h-5" />,
  //   path: '/dataWhitelist',
  // },
  // {
  //   name: '脚本更新',
  //   icon: <ScrollText className="w-5 h-5" />,
  //   path: '/updateScript',
  // },
  // {
  //   name: '版本管理',
  //   icon: <Rocket className="w-5 h-5" />,
  //   path: '/version',
  // },
  {
    name: '账号管理',
    icon: <UserRoundCog className="w-5 h-5" />,
    path: '/adminUser',
    isAdmin: true,
  },
    {
    name: '操作日志',
    icon: <ClipboardList className="w-5 h-5" />,
    path: '/actionLog',
  },
  {
    name: '设置',
    icon: <Rocket className="w-5 h-5" />,
    path: '/setting',
  },
];
