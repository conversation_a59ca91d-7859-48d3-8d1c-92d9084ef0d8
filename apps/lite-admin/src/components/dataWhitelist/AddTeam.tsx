import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@mono/ui/dialog';
import { LoadingButton } from '@/components/loading-button.tsx';
import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { teamList } from '@/api/team.ts';
import { Team } from '@/types/team.ts';
import { X } from 'lucide-react';
import { Input } from '@mono/ui/input';
import { Button } from '@mono/ui/button';
import { addTeam } from '@/api/dataWhitelist.ts';

interface AddTeamProps {
  open: boolean;
  onChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function AddTeam({ open, onChange, onSuccess }: AddTeamProps) {
  const [showAdd, setShowAdd] = useState(true);
  const [selectOpen, setSelectOpen] = useState(false);
  const [dataList, setDataList] = useState<Team[]>([]);
  const [selectItem, setSelectItem] = useState<Team[]>([]);

  const mutation = useMutation({
    mutationFn: (input: string) =>
      teamList({
        page: 1,
        size: 10,
        teamName: input,
      }),
    onSuccess: (data) => {
      setDataList(data?.data);
    },
  });

  const onEnter = (
    e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (e.key === 'Enter') {
      setShowAdd(false);
      const target = e.target as HTMLInputElement | HTMLTextAreaElement;
      mutation.mutate(target.value);
    }
  };

  const onBlur = () => {
    setTimeout(() => {
      setSelectOpen(false);
    }, 170);
  };

  const onSelectItem = (item: Team) => {
    if (selectItem.some((selected) => selected.id === item.id)) {
      return; // 如果已存在，直接返回
    }
    setSelectItem((prevItems) => [...prevItems, item]);
  };

  const submitMutation = useMutation({
    mutationFn: (input: Array<{ id: string; enabled: boolean }>) =>
      addTeam(input),
    onSuccess: () => {
      onSuccess?.();
      onChange(false);
    },
  });

  const onSubmit = () => {
    submitMutation.mutate(
      selectItem.map((item) => ({ id: item.id, enabled: true }))
    );
  };
  return (
    <>
      <div className={'fixed inset-0 z-50 bg-black/80'}></div>
      <Dialog open={open} onOpenChange={onChange} modal={false}>
        <DialogContent
          className="max-w-[620px]"
          onOpenAutoFocus={(e) => e.preventDefault()}
          onInteractOutside={(e) => e.preventDefault()}
        >
          <DialogHeader>
            <DialogTitle className={'mb-4'}>添加体验团队</DialogTitle>
            <div className={'flex flex-wrap items-center gap-3'}>
              {!showAdd && (
                <div className="relative mb-2">
                  <div className={'flex items-center gap-2'}>
                    <div className={'whitespace-nowrap'}>搜索团队：</div>
                    <Input
                      className={'z-10 relative w-[240px]'}
                      placeholder="请输入团队名称&ID"
                      onClick={() => setSelectOpen(true)}
                      onBlur={() => onBlur()}
                      onKeyDown={(e) => onEnter(e)}
                    />
                  </div>
                  <div
                    className={`absolute top-12 right-0 z-10 bg-white border w-[240px]  p-2 rounded-md ${selectOpen ? '' : 'hidden'}`}
                  >
                    <ul>
                      {dataList.length === 0 && (
                        <li className={'p-2 text-gray-500'}>暂无数据</li>
                      )}

                      {dataList.map((item) => (
                        <li
                          key={item.id}
                          className={
                            'flex items-center justify-between p-2 cursor-pointer hover:bg-gray-100'
                          }
                          onClick={() => onSelectItem(item)}
                        >
                          <span>{item.name}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {showAdd && (
                <Button onClick={() => setShowAdd(false)}>添加</Button>
              )}
            </div>
            <div className={'flex flex-wrap items-center gap-3'}>
              {selectItem?.map((item) => (
                <span
                  key={item.id}
                  className={
                    'rounded-sm px-2 py-1 bg-gray-300  flex items-center'
                  }
                >
                  {item.name}
                  <X size={16} className={'cursor-pointer'} />
                </span>
              ))}
            </div>
            <DialogDescription></DialogDescription>

            <DialogFooter className="">
              <Button
                type="button"
                variant="secondary"
                onClick={() => onChange(false)}
              >
                取消
              </Button>

              <LoadingButton
                type="submit"
                variant={'default'}
                onClick={onSubmit}
              >
                保存
              </LoadingButton>
            </DialogFooter>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
}
