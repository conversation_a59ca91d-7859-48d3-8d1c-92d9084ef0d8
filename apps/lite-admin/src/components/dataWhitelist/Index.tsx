import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@mono/ui/tooltip';
import { CircleHelp } from 'lucide-react';
import { useImmer } from 'use-immer';
import { Input } from '@mono/ui/input';
import { DatePickerWithRange } from '@/components/DatePickerWithRange.tsx';
import { Button } from '@mono/ui/button';
import { useMemo, useState } from 'react';
import { ColumnDef, PaginationState } from '@tanstack/react-table';
import { DataTable } from '@/components/dataTable.tsx';
import { useMutation, useQuery } from '@tanstack/react-query';
import { closeTeam, getList } from '@/api/dataWhitelist.ts';
import { format } from 'date-fns';
import { AddTeam } from '@/components/dataWhitelist/AddTeam.tsx';
import { DataWhitelist } from '@/types/dataWhitelist.ts';

interface FilterBase {
  teamName: string;
  phone: string;
  startTime: number | null;
  endTime: number | null;
}
interface FilterProps {
  onCreate: () => void;
  onSearchFilter: (filter: FilterBase) => void;
}

const TaskTip = () => {
  return (
    <>
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger asChild>
            <CircleHelp
              className={'ml-1 cursor-pointer'}
              size={16}
              color={'#9b9b9b'}
            />
          </TooltipTrigger>
          <TooltipContent>
            <p>该团队在云端的账号数据更新状态，数据更新后同步。</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
};

const FilterBar = ({ onCreate, onSearchFilter }: FilterProps) => {
  const [filter, setFilter] = useImmer<FilterBase>({
    teamName: '',
    phone: '',
    startTime: null,
    endTime: null,
  });

  const onSubmit = () => {
    onSearchFilter(filter);
  };

  return (
    <div className="flex flex-wrap gap-4 h-fit p-1">
      <Input
        className={'w-[220px]'}
        placeholder="请输入团队名称/ID"
        value={filter.teamName}
        onChange={(e) => {
          setFilter((draft) => {
            draft.teamName = e.target.value;
          });
        }}
      />
      <Input
        className={'w-[220px]'}
        placeholder="请输入手机号"
        value={filter.phone}
        onChange={(e) => {
          setFilter((draft) => {
            draft.phone = e.target.value;
          });
        }}
      />

      <div className="flex items-center gap-2 h-fit whitespace-nowrap">
        创建时间:
        <DatePickerWithRange
          className={'w-[240px]'}
          onChange={(date) => {
            setFilter((draft) => {
              draft.startTime =
                date.from ?
                  new Date(new Date(date.from).setHours(0, 0, 0, 0)).getTime()
                : null;
              draft.endTime =
                date.to ?
                  new Date(new Date(date.to).setHours(23, 59, 59, 0)).getTime()
                : null;
            });
          }}
        />
      </div>
      <Button onClick={() => onSubmit()}>搜索</Button>
      <Button onClick={onCreate}>添加团队</Button>
    </div>
  );
};

export const Index = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [filter, setFilter] = useImmer<FilterBase>({
    teamName: '',
    phone: '',
    startTime: null,
    endTime: null,
  });

  const [openAdd, setOpenAdd] = useState(false);

  const query = useQuery({
    queryKey: ['datalist', filter, pagination],
    queryFn: () => {
      return getList({
        page: pagination.pageIndex + 1,
        size: pagination.pageSize,
        ...filter,
      });
    },
  });

  const closeTeamMutation = useMutation({
    mutationFn: (input: Array<{ id: string; enabled: boolean }>) =>
      closeTeam(input),
    onSuccess: () => {
      void query.refetch();
    },
  });

  const onDelete = (id: string) => {
    closeTeamMutation.mutate([{ id, enabled: false }]);
  };

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-expect-error
  const columns = useMemo<Array<ColumnDef<DataWhitelist>>>(() => {
    return [
      {
        header: '团队名称&团队ID',
        accessorKey: 'orderNo',
        cell: ({ row }) => {
          const { name } = row.original;
          return <span>{name}</span>;
        },
      },
      {
        header: '手机号',
        accessorKey: 'a1',
        cell: ({ row }) => {
          const { phone } = row.original;
          return <span>{phone}</span>;
        },
      },
      {
        header: '团队账号数',
        accessorKey: 'a2',
        cell: ({ row }) => {
          const { accountCount } = row.original;
          return <span>{accountCount}</span>;
        },
      },
      {
        header: '添加时间',
        accessorKey: 'a3',
        cell: ({ row }) => {
          const { createdAt } = row.original;
          return (
            <span>
              {createdAt ? format(createdAt, 'yyyy-MM-dd HH:mm:ss') : '-'}
            </span>
          );
        },
      },
      {
        header: '数据最后更新时间',
        accessorKey: 'a4',
        cell: ({ row }) => {
          const { cloudDataUpdatedAt } = row.original;
          return (
            <span>
              {cloudDataUpdatedAt ?
                format(cloudDataUpdatedAt, 'yyyy-MM-dd HH:mm:ss')
              : '-'}
            </span>
          );
        },
      },
      {
        header: (
          <>
            <span className={'flex items-center gap-1'}>
              云端数据任务状态
              <TaskTip />
            </span>
          </>
        ),
        accessorKey: 'a5',
        cell: ({ row }) => {
          const { accountCount, accountOverviews } = row.original;
          return (
            <span>
              {accountOverviews}/{accountCount}
            </span>
          );
        },
      },
      {
        header: '操作',
        accessorKey: 'action',
        cell: ({ row }) => {
          return (
            <span
              className={'cursor-pointer text-red-500'}
              onClick={() => onDelete(row.original.id as string)}
            >
              删除
            </span>
          );
        },
      },
    ];
  });
  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      <div className="mb-4">
        <FilterBar
          onCreate={() => setOpenAdd(true)}
          onSearchFilter={setFilter}
        />
      </div>
      <DataTable
        columns={columns}
        data={query.data?.data}
        rowCount={query.data?.totalSize}
        pagination={pagination}
        setPagination={setPagination}
      />
      {openAdd && (
        <AddTeam
          open={openAdd}
          onChange={setOpenAdd}
          onSuccess={() => query.refetch()}
        />
      )}
    </div>
  );
};
