import { useQuery, useQueryClient } from "@tanstack/react-query";
import { ColumnDef, PaginationState } from "@tanstack/react-table";
// import { addDays } from "date-fns";
import { useState } from "react";
import { DataTable } from "../dataTable";
import { FigureItem, FigureResponse } from "@/types/figure";
import { Button } from "@mono/ui/button";
import { fetchFigure } from "@/api/figure";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@mono/ui/dropdown-menu";
import { formatDate } from '@mono/utils/day';
import { SearchBar } from './search'
import { ScrollArea } from "@mono/ui/scroll-area";

interface SearchParamsState {
  searchType: string;
  searchText: string;
  startDate?: number;
  endDate?: number;
  type: string;
  channel: string;
}

export function FigureIndex() {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [listType, setListType] = useState<1 | 2>(1); // 默认1（定制数字人）
  const [selectedVoice, setSelectedVoice] = useState<FigureItem | null>(null);
  const [showDetail, setShowDetail] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchParamsState>({
    searchType: '1',
    searchText: '',
    startDate: undefined,
    endDate: undefined,
    type: '',
    channel: '',
  });


  const queryClient = useQueryClient();
  const { data: voice } = useQuery<FigureResponse>({
    queryKey: ['fetchFigure', { ...searchParams, listType,
      pageIndex:pagination.pageIndex,
      pageSize:pagination.pageSize,
     }],
    queryFn: () => fetchFigure({
      ...searchParams,
      listType,
      page:pagination.pageIndex+1,
      size:pagination.pageSize
    }),
  });


  const resetSearchParams = () => {
    setSearchParams({
      searchType: '1',
      searchText: '',
      startDate: undefined,
      endDate: undefined,
      type: '',
      channel: '',
    });
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  };

  const switchToListType = (type: 1 | 2) => {
    setListType(type);
    resetSearchParams();
  };

  const StatusBadge = ({ status }: { status?: string }) => {
    if (!status) return null;

    const statusConfig: Record<string, { label: string; colorClass: string }> = {
      training_success: { label: '训练成功', colorClass: 'text-green-500' },
      training: { label: '训练中', colorClass: 'text-blue-500' },
      training_fail: { label: '训练失败', colorClass: 'text-red-500' },
      censoring: { label: '审核中', colorClass: 'text-yellow-500' },
    };

    const { label, colorClass } = statusConfig[status] || { label: '--', colorClass: 'text-gray-500' };

    return (
      <div className="flex items-center">
        <span className={`mr-1 text-lg ${colorClass}`}>•</span>
        <span>{label}</span>
      </div>
    );
  };



  //徽章
  const TypeBadge = ({ type = 'unknown' }: { type?: string }) => {
    const typeConfig = {
      lite: {
        className: "bg-green-50 text-green-600 border border-green-100",
        text: "极速版",
      },
      pro: {
        className: "bg-orange-50 text-orange-600 border border-orange-100",
        text: "专业版",
      },
      unknown: {
        className: "bg-gray-100 text-gray-600",
        text: "未知",
      },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.unknown;

    return (
      <span className={`px-2 py-1 text-xs ${config.className}`}>
        {config.text}
      </span>
    );
  };

  const handleViewDetail = (voiceItem: FigureItem) => {
    setSelectedVoice(voiceItem);
    setShowDetail(true);
  };

  const handleBackToList = () => {
    setShowDetail(false);
    setSelectedVoice(null);
  };

  const voiceColumns: ColumnDef<FigureItem>[] = [
    {
      header: '预览',
      accessorKey: 'cover',
      cell: ({ row }) => <span><img src={row.original.cover} className="w-14" /></span>
    },
    {
      header: '数字人名称',
      accessorKey: 'name',
    },
    {
      header: "类型",
      accessorKey: "type",
      cell: ({ row }) => <TypeBadge type={row.original.type} />,
    },
    {
      header: '用户手机号',
      accessorKey: 'userPhone',
      cell: ({ row }) => row.original.userPhone || '-',
    },
    {
      header: '交付时间',
      accessorKey: 'updatedAt',
      cell: ({ row }) => formatDate(new Date(row.original.updatedAt!).getTime()),
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="link" className="text-blue-500 p-0">
            复制ID
          </Button>
          <Button
            variant="link"
            className="text-blue-500 p-0"
            onClick={() => handleViewDetail(row.original)}
          >
            查看详情
          </Button>
        </div>
      ),
    },
  ];

  const recordColumns: ColumnDef<FigureItem>[] = [
    {
      header: '数字人名称',
      accessorKey: 'name',
    },
    {
      header: "类型",
      accessorKey: "type",
      cell: ({ row }) => <TypeBadge type={row.original.type} />,
    },
    {
      header: '用户手机号',
      accessorKey: 'userPhone',
      cell: ({ row }) => row.original.userPhone || '-',
    },
    {
      header: '提交时间',
      accessorKey: 'createdAt',
      cell: ({ row }) => formatDate(new Date(row.original.createdAt!).getTime()),
    },
    {
      header: '状态',
      accessorKey: 'status',
      cell: ({ row }) => <StatusBadge status={row.original.status} />,
    },
    {
      header: '操作',
      cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="link" className="text-blue-500 p-0">
            复制ID
          </Button>
          {
            row.original.status == 'failed' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild className="border-none">
                  <Button variant="link" className="p-0 text-blue-500">
                    查看原因
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent side="bottom" className="flex items-center justify-center w-full">
                  <DropdownMenuItem>
                    {row.original.reason}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )
          }
        </div>
      ),
    },
  ];

  const renderDetailView = () => {
    if (!selectedVoice) return null;

    return (
      <div className="p-6 rounded-lg border">
        <div className="space-y-6 text-sm ">

          <div className="space-y-8">
            <div className="flex items-start">
              <p className="w-32 text-gray-500">数字人名称:</p>
              <p className="flex-1 font-medium">{selectedVoice.name}</p>
            </div>

            <div className="flex items-start ">
              <p className="w-32 text-gray-500 ">用户手机号:</p>
              <p className="flex-1">{selectedVoice.userPhone || '-'}</p>
            </div>


            <div className="flex items-start text-gray-400 text-center">
              <div className="flex-1 flex items-center gap-8">



                <div className="flex flex-col gap-4 items-center">
                  {/* 增加视频空域部分用黑边代替 */}
                  <div className="w-[200px] h-[350px] bg-black rounded-md overflow-hidden relative border">
                    <video
                      className="absolute top-1/2 left-1/2 w-full h-full object-contain transform -translate-x-1/2 -translate-y-1/2"
                      src={selectedVoice.authVideoUrl}
                      controls
                    />
                  </div>
                  <span>训练视频</span>
                </div>

                <div className="flex flex-col gap-4 items-center">
                  <div className="w-[200px] h-[350px] bg-black rounded-md overflow-hidden relative border">
                    <video
                      className="absolute top-1/2 left-1/2 w-full h-full object-contain transform -translate-x-1/2 -translate-y-1/2"
                      src={selectedVoice.originalVideoUrl}
                      controls
                    />
                  </div>
                  <span>授权视频</span>
                </div>


                <div className="flex flex-col gap-4">
                  <img
                    className="w-[12.2rem]  rounded-md border"
                    src="https://bhb-frontend.bhbcdn.com/static/files/a08e41cbf7c142788b8caabfcbe78c90.svg"
                  />
                  <text>暂无样片</text>
                </div>
              </div>
            </div>




            <div className="flex items-start">
              <p className="w-32 text-gray-500">创建时间:</p>
              <p className="flex-1">
                {formatDate(new Date(selectedVoice.createdAt!).getTime())}
              </p>
            </div>


            <div className="flex items-start">
              <p className="w-32 text-gray-500">交付时间:</p>
              <p className="flex-1">
                {formatDate(new Date(selectedVoice.updatedAt!).getTime())}
              </p>
            </div>


            {/* <div className="flex items-start">
              <p className="w-32 text-gray-500">审核用户备注:</p>
              <p className="flex-1 text-gray-400">
                {selectedVoice.remark || '--'}
              </p>
            </div> */}
          </div>
        </div>
      </div>
    );
  };

  return (
    <ScrollArea className="h-full w-full" enableHorizontal>
      <div className="flex flex-col gap-4 p-4">
        {showDetail ? (
          <div className="flex items-center text-sm  mb-2">
            <span
              className="text-gray-500 cursor-pointer hover:underline"
              onClick={() => {
                handleBackToList();
                resetSearchParams();
              }}
            >
              定制数字人
            </span>
            <span className="mx-2">/</span>
            <span>数字人详情</span>
          </div>
        ) : listType === 2 && (
          <div className="flex items-center text-sm mb-2">
            <span
              className="text-gray-500 cursor-pointer hover:underline"
              onClick={() => switchToListType(1)}
            >
              定制数字人
            </span>
            <span className="mx-2">/</span>
            <span>定制记录</span>
          </div>
        )}

        <h1 className="text-xl font-bold">
          {showDetail ? '基础信息' : (listType === 1 ? '数字人列表' : '定制列表')}
        </h1>


        {!showDetail &&
          <SearchBar
            onSearch={(params) => {
              setSearchParams({
                ...searchParams,
                searchType: params.searchType,
                searchText: params.searchText,
                type: params.type,
                channel: params.channel,
                startDate: params.dateRange?.from?.getTime(),
                endDate: params.dateRange?.to?.getTime(),
              });
              queryClient.invalidateQueries({ queryKey: ['fetchFigure'] });
            }}
            onReset={() => {
              setSearchParams({
                searchType: '1',
                searchText: '',
                startDate: undefined,
                endDate: undefined,
                type: '',
                channel: '',
              });
              queryClient.invalidateQueries({ queryKey: ['fetchFigure'] });
            }}
          />
        }


        {!showDetail && listType === 1 && (
          <Button
            className="w-[110px] text-xs"
            onClick={() => switchToListType(2)}
          >
            查看定制记录
          </Button>
        )}



        {showDetail ? (
          renderDetailView()
        ) : (
          <DataTable
            columns={listType === 1 ? voiceColumns : recordColumns}
            data={voice?.data || []}
            rowCount={voice?.totalSize}
            pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
            }}
            setPagination={setPagination}
            
          />
        )}
      </div>
    </ScrollArea>
  );
}