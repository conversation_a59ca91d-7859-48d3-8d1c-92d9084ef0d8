/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as HomeImport } from './routes/_home'
import { Route as IndexImport } from './routes/index'
import { Route as HomeVoiceImport } from './routes/_home/voice'
import { Route as HomeVersionImport } from './routes/_home/version'
import { Route as HomeUserOrderImport } from './routes/_home/userOrder'
import { Route as HomeUpdateScriptImport } from './routes/_home/updateScript'
import { Route as HomeTeamImport } from './routes/_home/team'
import { Route as HomeSystemNotifyImport } from './routes/_home/systemNotify'
import { Route as HomeSoundCloneImport } from './routes/_home/soundClone'
import { Route as HomeSettingImport } from './routes/_home/setting'
import { Route as HomePlatformAccountImport } from './routes/_home/platformAccount'
import { Route as HomePerformanceAnalysisImport } from './routes/_home/performance-analysis'
import { Route as HomeOrderImport } from './routes/_home/order'
import { Route as HomeOnlineStatisticsImport } from './routes/_home/online-statistics'
import { Route as HomeGenerateVideoImport } from './routes/_home/generateVideo'
import { Route as HomeFigureImport } from './routes/_home/figure'
import { Route as HomeFastCloneImport } from './routes/_home/fastClone'
import { Route as HomeExchangePackageImport } from './routes/_home/exchangePackage'
import { Route as HomeDistManageImport } from './routes/_home/distManage'
import { Route as HomeDataWhitelistImport } from './routes/_home/dataWhitelist'
import { Route as HomeDataImport } from './routes/_home/data'
import { Route as HomeCustomizedAICharacterImport } from './routes/_home/customizedAICharacter'
import { Route as HomeComputeDemandImport } from './routes/_home/computeDemand'
import { Route as HomeComputeCenterImport } from './routes/_home/computeCenter'
import { Route as HomeClientUserImport } from './routes/_home/clientUser'
import { Route as HomeChannelManageDetailImport } from './routes/_home/channelManageDetail'
import { Route as HomeChannelManageImport } from './routes/_home/channelManage'
import { Route as HomeChannelImport } from './routes/_home/channel'
import { Route as HomeAdminUserImport } from './routes/_home/adminUser'
import { Route as HomeAdImport } from './routes/_home/ad'
import { Route as HomeActionLogImport } from './routes/_home/actionLog'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const HomeRoute = HomeImport.update({
  id: '/_home',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const HomeVoiceRoute = HomeVoiceImport.update({
  id: '/voice',
  path: '/voice',
  getParentRoute: () => HomeRoute,
} as any)

const HomeVersionRoute = HomeVersionImport.update({
  id: '/version',
  path: '/version',
  getParentRoute: () => HomeRoute,
} as any)

const HomeUserOrderRoute = HomeUserOrderImport.update({
  id: '/userOrder',
  path: '/userOrder',
  getParentRoute: () => HomeRoute,
} as any)

const HomeUpdateScriptRoute = HomeUpdateScriptImport.update({
  id: '/updateScript',
  path: '/updateScript',
  getParentRoute: () => HomeRoute,
} as any)

const HomeTeamRoute = HomeTeamImport.update({
  id: '/team',
  path: '/team',
  getParentRoute: () => HomeRoute,
} as any)

const HomeSystemNotifyRoute = HomeSystemNotifyImport.update({
  id: '/systemNotify',
  path: '/systemNotify',
  getParentRoute: () => HomeRoute,
} as any)

const HomeSoundCloneRoute = HomeSoundCloneImport.update({
  id: '/soundClone',
  path: '/soundClone',
  getParentRoute: () => HomeRoute,
} as any)

const HomeSettingRoute = HomeSettingImport.update({
  id: '/setting',
  path: '/setting',
  getParentRoute: () => HomeRoute,
} as any)

const HomePlatformAccountRoute = HomePlatformAccountImport.update({
  id: '/platformAccount',
  path: '/platformAccount',
  getParentRoute: () => HomeRoute,
} as any)

const HomePerformanceAnalysisRoute = HomePerformanceAnalysisImport.update({
  id: '/performance-analysis',
  path: '/performance-analysis',
  getParentRoute: () => HomeRoute,
} as any)

const HomeOrderRoute = HomeOrderImport.update({
  id: '/order',
  path: '/order',
  getParentRoute: () => HomeRoute,
} as any)

const HomeOnlineStatisticsRoute = HomeOnlineStatisticsImport.update({
  id: '/online-statistics',
  path: '/online-statistics',
  getParentRoute: () => HomeRoute,
} as any)

const HomeGenerateVideoRoute = HomeGenerateVideoImport.update({
  id: '/generateVideo',
  path: '/generateVideo',
  getParentRoute: () => HomeRoute,
} as any)

const HomeFigureRoute = HomeFigureImport.update({
  id: '/figure',
  path: '/figure',
  getParentRoute: () => HomeRoute,
} as any)

const HomeFastCloneRoute = HomeFastCloneImport.update({
  id: '/fastClone',
  path: '/fastClone',
  getParentRoute: () => HomeRoute,
} as any)

const HomeExchangePackageRoute = HomeExchangePackageImport.update({
  id: '/exchangePackage',
  path: '/exchangePackage',
  getParentRoute: () => HomeRoute,
} as any)

const HomeDistManageRoute = HomeDistManageImport.update({
  id: '/distManage',
  path: '/distManage',
  getParentRoute: () => HomeRoute,
} as any)

const HomeDataWhitelistRoute = HomeDataWhitelistImport.update({
  id: '/dataWhitelist',
  path: '/dataWhitelist',
  getParentRoute: () => HomeRoute,
} as any)

const HomeDataRoute = HomeDataImport.update({
  id: '/data',
  path: '/data',
  getParentRoute: () => HomeRoute,
} as any)

const HomeCustomizedAICharacterRoute = HomeCustomizedAICharacterImport.update({
  id: '/customizedAICharacter',
  path: '/customizedAICharacter',
  getParentRoute: () => HomeRoute,
} as any)

const HomeComputeDemandRoute = HomeComputeDemandImport.update({
  id: '/computeDemand',
  path: '/computeDemand',
  getParentRoute: () => HomeRoute,
} as any)

const HomeComputeCenterRoute = HomeComputeCenterImport.update({
  id: '/computeCenter',
  path: '/computeCenter',
  getParentRoute: () => HomeRoute,
} as any)

const HomeClientUserRoute = HomeClientUserImport.update({
  id: '/clientUser',
  path: '/clientUser',
  getParentRoute: () => HomeRoute,
} as any)

const HomeChannelManageDetailRoute = HomeChannelManageDetailImport.update({
  id: '/channelManageDetail',
  path: '/channelManageDetail',
  getParentRoute: () => HomeRoute,
} as any)

const HomeChannelManageRoute = HomeChannelManageImport.update({
  id: '/channelManage',
  path: '/channelManage',
  getParentRoute: () => HomeRoute,
} as any)

const HomeChannelRoute = HomeChannelImport.update({
  id: '/channel',
  path: '/channel',
  getParentRoute: () => HomeRoute,
} as any)

const HomeAdminUserRoute = HomeAdminUserImport.update({
  id: '/adminUser',
  path: '/adminUser',
  getParentRoute: () => HomeRoute,
} as any)

const HomeAdRoute = HomeAdImport.update({
  id: '/ad',
  path: '/ad',
  getParentRoute: () => HomeRoute,
} as any)

const HomeActionLogRoute = HomeActionLogImport.update({
  id: '/actionLog',
  path: '/actionLog',
  getParentRoute: () => HomeRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/_home': {
      id: '/_home'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof HomeImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/_home/actionLog': {
      id: '/_home/actionLog'
      path: '/actionLog'
      fullPath: '/actionLog'
      preLoaderRoute: typeof HomeActionLogImport
      parentRoute: typeof HomeImport
    }
    '/_home/ad': {
      id: '/_home/ad'
      path: '/ad'
      fullPath: '/ad'
      preLoaderRoute: typeof HomeAdImport
      parentRoute: typeof HomeImport
    }
    '/_home/adminUser': {
      id: '/_home/adminUser'
      path: '/adminUser'
      fullPath: '/adminUser'
      preLoaderRoute: typeof HomeAdminUserImport
      parentRoute: typeof HomeImport
    }
    '/_home/channel': {
      id: '/_home/channel'
      path: '/channel'
      fullPath: '/channel'
      preLoaderRoute: typeof HomeChannelImport
      parentRoute: typeof HomeImport
    }
    '/_home/channelManage': {
      id: '/_home/channelManage'
      path: '/channelManage'
      fullPath: '/channelManage'
      preLoaderRoute: typeof HomeChannelManageImport
      parentRoute: typeof HomeImport
    }
    '/_home/channelManageDetail': {
      id: '/_home/channelManageDetail'
      path: '/channelManageDetail'
      fullPath: '/channelManageDetail'
      preLoaderRoute: typeof HomeChannelManageDetailImport
      parentRoute: typeof HomeImport
    }
    '/_home/clientUser': {
      id: '/_home/clientUser'
      path: '/clientUser'
      fullPath: '/clientUser'
      preLoaderRoute: typeof HomeClientUserImport
      parentRoute: typeof HomeImport
    }
    '/_home/computeCenter': {
      id: '/_home/computeCenter'
      path: '/computeCenter'
      fullPath: '/computeCenter'
      preLoaderRoute: typeof HomeComputeCenterImport
      parentRoute: typeof HomeImport
    }
    '/_home/computeDemand': {
      id: '/_home/computeDemand'
      path: '/computeDemand'
      fullPath: '/computeDemand'
      preLoaderRoute: typeof HomeComputeDemandImport
      parentRoute: typeof HomeImport
    }
    '/_home/customizedAICharacter': {
      id: '/_home/customizedAICharacter'
      path: '/customizedAICharacter'
      fullPath: '/customizedAICharacter'
      preLoaderRoute: typeof HomeCustomizedAICharacterImport
      parentRoute: typeof HomeImport
    }
    '/_home/data': {
      id: '/_home/data'
      path: '/data'
      fullPath: '/data'
      preLoaderRoute: typeof HomeDataImport
      parentRoute: typeof HomeImport
    }
    '/_home/dataWhitelist': {
      id: '/_home/dataWhitelist'
      path: '/dataWhitelist'
      fullPath: '/dataWhitelist'
      preLoaderRoute: typeof HomeDataWhitelistImport
      parentRoute: typeof HomeImport
    }
    '/_home/distManage': {
      id: '/_home/distManage'
      path: '/distManage'
      fullPath: '/distManage'
      preLoaderRoute: typeof HomeDistManageImport
      parentRoute: typeof HomeImport
    }
    '/_home/exchangePackage': {
      id: '/_home/exchangePackage'
      path: '/exchangePackage'
      fullPath: '/exchangePackage'
      preLoaderRoute: typeof HomeExchangePackageImport
      parentRoute: typeof HomeImport
    }
    '/_home/fastClone': {
      id: '/_home/fastClone'
      path: '/fastClone'
      fullPath: '/fastClone'
      preLoaderRoute: typeof HomeFastCloneImport
      parentRoute: typeof HomeImport
    }
    '/_home/figure': {
      id: '/_home/figure'
      path: '/figure'
      fullPath: '/figure'
      preLoaderRoute: typeof HomeFigureImport
      parentRoute: typeof HomeImport
    }
    '/_home/generateVideo': {
      id: '/_home/generateVideo'
      path: '/generateVideo'
      fullPath: '/generateVideo'
      preLoaderRoute: typeof HomeGenerateVideoImport
      parentRoute: typeof HomeImport
    }
    '/_home/online-statistics': {
      id: '/_home/online-statistics'
      path: '/online-statistics'
      fullPath: '/online-statistics'
      preLoaderRoute: typeof HomeOnlineStatisticsImport
      parentRoute: typeof HomeImport
    }
    '/_home/order': {
      id: '/_home/order'
      path: '/order'
      fullPath: '/order'
      preLoaderRoute: typeof HomeOrderImport
      parentRoute: typeof HomeImport
    }
    '/_home/performance-analysis': {
      id: '/_home/performance-analysis'
      path: '/performance-analysis'
      fullPath: '/performance-analysis'
      preLoaderRoute: typeof HomePerformanceAnalysisImport
      parentRoute: typeof HomeImport
    }
    '/_home/platformAccount': {
      id: '/_home/platformAccount'
      path: '/platformAccount'
      fullPath: '/platformAccount'
      preLoaderRoute: typeof HomePlatformAccountImport
      parentRoute: typeof HomeImport
    }
    '/_home/setting': {
      id: '/_home/setting'
      path: '/setting'
      fullPath: '/setting'
      preLoaderRoute: typeof HomeSettingImport
      parentRoute: typeof HomeImport
    }
    '/_home/soundClone': {
      id: '/_home/soundClone'
      path: '/soundClone'
      fullPath: '/soundClone'
      preLoaderRoute: typeof HomeSoundCloneImport
      parentRoute: typeof HomeImport
    }
    '/_home/systemNotify': {
      id: '/_home/systemNotify'
      path: '/systemNotify'
      fullPath: '/systemNotify'
      preLoaderRoute: typeof HomeSystemNotifyImport
      parentRoute: typeof HomeImport
    }
    '/_home/team': {
      id: '/_home/team'
      path: '/team'
      fullPath: '/team'
      preLoaderRoute: typeof HomeTeamImport
      parentRoute: typeof HomeImport
    }
    '/_home/updateScript': {
      id: '/_home/updateScript'
      path: '/updateScript'
      fullPath: '/updateScript'
      preLoaderRoute: typeof HomeUpdateScriptImport
      parentRoute: typeof HomeImport
    }
    '/_home/userOrder': {
      id: '/_home/userOrder'
      path: '/userOrder'
      fullPath: '/userOrder'
      preLoaderRoute: typeof HomeUserOrderImport
      parentRoute: typeof HomeImport
    }
    '/_home/version': {
      id: '/_home/version'
      path: '/version'
      fullPath: '/version'
      preLoaderRoute: typeof HomeVersionImport
      parentRoute: typeof HomeImport
    }
    '/_home/voice': {
      id: '/_home/voice'
      path: '/voice'
      fullPath: '/voice'
      preLoaderRoute: typeof HomeVoiceImport
      parentRoute: typeof HomeImport
    }
  }
}

// Create and export the route tree

interface HomeRouteChildren {
  HomeActionLogRoute: typeof HomeActionLogRoute
  HomeAdRoute: typeof HomeAdRoute
  HomeAdminUserRoute: typeof HomeAdminUserRoute
  HomeChannelRoute: typeof HomeChannelRoute
  HomeChannelManageRoute: typeof HomeChannelManageRoute
  HomeChannelManageDetailRoute: typeof HomeChannelManageDetailRoute
  HomeClientUserRoute: typeof HomeClientUserRoute
  HomeComputeCenterRoute: typeof HomeComputeCenterRoute
  HomeComputeDemandRoute: typeof HomeComputeDemandRoute
  HomeCustomizedAICharacterRoute: typeof HomeCustomizedAICharacterRoute
  HomeDataRoute: typeof HomeDataRoute
  HomeDataWhitelistRoute: typeof HomeDataWhitelistRoute
  HomeDistManageRoute: typeof HomeDistManageRoute
  HomeExchangePackageRoute: typeof HomeExchangePackageRoute
  HomeFastCloneRoute: typeof HomeFastCloneRoute
  HomeFigureRoute: typeof HomeFigureRoute
  HomeGenerateVideoRoute: typeof HomeGenerateVideoRoute
  HomeOnlineStatisticsRoute: typeof HomeOnlineStatisticsRoute
  HomeOrderRoute: typeof HomeOrderRoute
  HomePerformanceAnalysisRoute: typeof HomePerformanceAnalysisRoute
  HomePlatformAccountRoute: typeof HomePlatformAccountRoute
  HomeSettingRoute: typeof HomeSettingRoute
  HomeSoundCloneRoute: typeof HomeSoundCloneRoute
  HomeSystemNotifyRoute: typeof HomeSystemNotifyRoute
  HomeTeamRoute: typeof HomeTeamRoute
  HomeUpdateScriptRoute: typeof HomeUpdateScriptRoute
  HomeUserOrderRoute: typeof HomeUserOrderRoute
  HomeVersionRoute: typeof HomeVersionRoute
  HomeVoiceRoute: typeof HomeVoiceRoute
}

const HomeRouteChildren: HomeRouteChildren = {
  HomeActionLogRoute: HomeActionLogRoute,
  HomeAdRoute: HomeAdRoute,
  HomeAdminUserRoute: HomeAdminUserRoute,
  HomeChannelRoute: HomeChannelRoute,
  HomeChannelManageRoute: HomeChannelManageRoute,
  HomeChannelManageDetailRoute: HomeChannelManageDetailRoute,
  HomeClientUserRoute: HomeClientUserRoute,
  HomeComputeCenterRoute: HomeComputeCenterRoute,
  HomeComputeDemandRoute: HomeComputeDemandRoute,
  HomeCustomizedAICharacterRoute: HomeCustomizedAICharacterRoute,
  HomeDataRoute: HomeDataRoute,
  HomeDataWhitelistRoute: HomeDataWhitelistRoute,
  HomeDistManageRoute: HomeDistManageRoute,
  HomeExchangePackageRoute: HomeExchangePackageRoute,
  HomeFastCloneRoute: HomeFastCloneRoute,
  HomeFigureRoute: HomeFigureRoute,
  HomeGenerateVideoRoute: HomeGenerateVideoRoute,
  HomeOnlineStatisticsRoute: HomeOnlineStatisticsRoute,
  HomeOrderRoute: HomeOrderRoute,
  HomePerformanceAnalysisRoute: HomePerformanceAnalysisRoute,
  HomePlatformAccountRoute: HomePlatformAccountRoute,
  HomeSettingRoute: HomeSettingRoute,
  HomeSoundCloneRoute: HomeSoundCloneRoute,
  HomeSystemNotifyRoute: HomeSystemNotifyRoute,
  HomeTeamRoute: HomeTeamRoute,
  HomeUpdateScriptRoute: HomeUpdateScriptRoute,
  HomeUserOrderRoute: HomeUserOrderRoute,
  HomeVersionRoute: HomeVersionRoute,
  HomeVoiceRoute: HomeVoiceRoute,
}

const HomeRouteWithChildren = HomeRoute._addFileChildren(HomeRouteChildren)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof HomeRouteWithChildren
  '/login': typeof LoginRoute
  '/actionLog': typeof HomeActionLogRoute
  '/ad': typeof HomeAdRoute
  '/adminUser': typeof HomeAdminUserRoute
  '/channel': typeof HomeChannelRoute
  '/channelManage': typeof HomeChannelManageRoute
  '/channelManageDetail': typeof HomeChannelManageDetailRoute
  '/clientUser': typeof HomeClientUserRoute
  '/computeCenter': typeof HomeComputeCenterRoute
  '/computeDemand': typeof HomeComputeDemandRoute
  '/customizedAICharacter': typeof HomeCustomizedAICharacterRoute
  '/data': typeof HomeDataRoute
  '/dataWhitelist': typeof HomeDataWhitelistRoute
  '/distManage': typeof HomeDistManageRoute
  '/exchangePackage': typeof HomeExchangePackageRoute
  '/fastClone': typeof HomeFastCloneRoute
  '/figure': typeof HomeFigureRoute
  '/generateVideo': typeof HomeGenerateVideoRoute
  '/online-statistics': typeof HomeOnlineStatisticsRoute
  '/order': typeof HomeOrderRoute
  '/performance-analysis': typeof HomePerformanceAnalysisRoute
  '/platformAccount': typeof HomePlatformAccountRoute
  '/setting': typeof HomeSettingRoute
  '/soundClone': typeof HomeSoundCloneRoute
  '/systemNotify': typeof HomeSystemNotifyRoute
  '/team': typeof HomeTeamRoute
  '/updateScript': typeof HomeUpdateScriptRoute
  '/userOrder': typeof HomeUserOrderRoute
  '/version': typeof HomeVersionRoute
  '/voice': typeof HomeVoiceRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof HomeRouteWithChildren
  '/login': typeof LoginRoute
  '/actionLog': typeof HomeActionLogRoute
  '/ad': typeof HomeAdRoute
  '/adminUser': typeof HomeAdminUserRoute
  '/channel': typeof HomeChannelRoute
  '/channelManage': typeof HomeChannelManageRoute
  '/channelManageDetail': typeof HomeChannelManageDetailRoute
  '/clientUser': typeof HomeClientUserRoute
  '/computeCenter': typeof HomeComputeCenterRoute
  '/computeDemand': typeof HomeComputeDemandRoute
  '/customizedAICharacter': typeof HomeCustomizedAICharacterRoute
  '/data': typeof HomeDataRoute
  '/dataWhitelist': typeof HomeDataWhitelistRoute
  '/distManage': typeof HomeDistManageRoute
  '/exchangePackage': typeof HomeExchangePackageRoute
  '/fastClone': typeof HomeFastCloneRoute
  '/figure': typeof HomeFigureRoute
  '/generateVideo': typeof HomeGenerateVideoRoute
  '/online-statistics': typeof HomeOnlineStatisticsRoute
  '/order': typeof HomeOrderRoute
  '/performance-analysis': typeof HomePerformanceAnalysisRoute
  '/platformAccount': typeof HomePlatformAccountRoute
  '/setting': typeof HomeSettingRoute
  '/soundClone': typeof HomeSoundCloneRoute
  '/systemNotify': typeof HomeSystemNotifyRoute
  '/team': typeof HomeTeamRoute
  '/updateScript': typeof HomeUpdateScriptRoute
  '/userOrder': typeof HomeUserOrderRoute
  '/version': typeof HomeVersionRoute
  '/voice': typeof HomeVoiceRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/_home': typeof HomeRouteWithChildren
  '/login': typeof LoginRoute
  '/_home/actionLog': typeof HomeActionLogRoute
  '/_home/ad': typeof HomeAdRoute
  '/_home/adminUser': typeof HomeAdminUserRoute
  '/_home/channel': typeof HomeChannelRoute
  '/_home/channelManage': typeof HomeChannelManageRoute
  '/_home/channelManageDetail': typeof HomeChannelManageDetailRoute
  '/_home/clientUser': typeof HomeClientUserRoute
  '/_home/computeCenter': typeof HomeComputeCenterRoute
  '/_home/computeDemand': typeof HomeComputeDemandRoute
  '/_home/customizedAICharacter': typeof HomeCustomizedAICharacterRoute
  '/_home/data': typeof HomeDataRoute
  '/_home/dataWhitelist': typeof HomeDataWhitelistRoute
  '/_home/distManage': typeof HomeDistManageRoute
  '/_home/exchangePackage': typeof HomeExchangePackageRoute
  '/_home/fastClone': typeof HomeFastCloneRoute
  '/_home/figure': typeof HomeFigureRoute
  '/_home/generateVideo': typeof HomeGenerateVideoRoute
  '/_home/online-statistics': typeof HomeOnlineStatisticsRoute
  '/_home/order': typeof HomeOrderRoute
  '/_home/performance-analysis': typeof HomePerformanceAnalysisRoute
  '/_home/platformAccount': typeof HomePlatformAccountRoute
  '/_home/setting': typeof HomeSettingRoute
  '/_home/soundClone': typeof HomeSoundCloneRoute
  '/_home/systemNotify': typeof HomeSystemNotifyRoute
  '/_home/team': typeof HomeTeamRoute
  '/_home/updateScript': typeof HomeUpdateScriptRoute
  '/_home/userOrder': typeof HomeUserOrderRoute
  '/_home/version': typeof HomeVersionRoute
  '/_home/voice': typeof HomeVoiceRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/actionLog'
    | '/ad'
    | '/adminUser'
    | '/channel'
    | '/channelManage'
    | '/channelManageDetail'
    | '/clientUser'
    | '/computeCenter'
    | '/computeDemand'
    | '/customizedAICharacter'
    | '/data'
    | '/dataWhitelist'
    | '/distManage'
    | '/exchangePackage'
    | '/fastClone'
    | '/figure'
    | '/generateVideo'
    | '/online-statistics'
    | '/order'
    | '/performance-analysis'
    | '/platformAccount'
    | '/setting'
    | '/soundClone'
    | '/systemNotify'
    | '/team'
    | '/updateScript'
    | '/userOrder'
    | '/version'
    | '/voice'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/login'
    | '/actionLog'
    | '/ad'
    | '/adminUser'
    | '/channel'
    | '/channelManage'
    | '/channelManageDetail'
    | '/clientUser'
    | '/computeCenter'
    | '/computeDemand'
    | '/customizedAICharacter'
    | '/data'
    | '/dataWhitelist'
    | '/distManage'
    | '/exchangePackage'
    | '/fastClone'
    | '/figure'
    | '/generateVideo'
    | '/online-statistics'
    | '/order'
    | '/performance-analysis'
    | '/platformAccount'
    | '/setting'
    | '/soundClone'
    | '/systemNotify'
    | '/team'
    | '/updateScript'
    | '/userOrder'
    | '/version'
    | '/voice'
  id:
    | '__root__'
    | '/'
    | '/_home'
    | '/login'
    | '/_home/actionLog'
    | '/_home/ad'
    | '/_home/adminUser'
    | '/_home/channel'
    | '/_home/channelManage'
    | '/_home/channelManageDetail'
    | '/_home/clientUser'
    | '/_home/computeCenter'
    | '/_home/computeDemand'
    | '/_home/customizedAICharacter'
    | '/_home/data'
    | '/_home/dataWhitelist'
    | '/_home/distManage'
    | '/_home/exchangePackage'
    | '/_home/fastClone'
    | '/_home/figure'
    | '/_home/generateVideo'
    | '/_home/online-statistics'
    | '/_home/order'
    | '/_home/performance-analysis'
    | '/_home/platformAccount'
    | '/_home/setting'
    | '/_home/soundClone'
    | '/_home/systemNotify'
    | '/_home/team'
    | '/_home/updateScript'
    | '/_home/userOrder'
    | '/_home/version'
    | '/_home/voice'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  HomeRoute: typeof HomeRouteWithChildren
  LoginRoute: typeof LoginRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  HomeRoute: HomeRouteWithChildren,
  LoginRoute: LoginRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/_home",
        "/login"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/_home": {
      "filePath": "_home.tsx",
      "children": [
        "/_home/actionLog",
        "/_home/ad",
        "/_home/adminUser",
        "/_home/channel",
        "/_home/channelManage",
        "/_home/channelManageDetail",
        "/_home/clientUser",
        "/_home/computeCenter",
        "/_home/computeDemand",
        "/_home/customizedAICharacter",
        "/_home/data",
        "/_home/dataWhitelist",
        "/_home/distManage",
        "/_home/exchangePackage",
        "/_home/fastClone",
        "/_home/figure",
        "/_home/generateVideo",
        "/_home/online-statistics",
        "/_home/order",
        "/_home/performance-analysis",
        "/_home/platformAccount",
        "/_home/setting",
        "/_home/soundClone",
        "/_home/systemNotify",
        "/_home/team",
        "/_home/updateScript",
        "/_home/userOrder",
        "/_home/version",
        "/_home/voice"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/_home/actionLog": {
      "filePath": "_home/actionLog.tsx",
      "parent": "/_home"
    },
    "/_home/ad": {
      "filePath": "_home/ad.tsx",
      "parent": "/_home"
    },
    "/_home/adminUser": {
      "filePath": "_home/adminUser.tsx",
      "parent": "/_home"
    },
    "/_home/channel": {
      "filePath": "_home/channel.tsx",
      "parent": "/_home"
    },
    "/_home/channelManage": {
      "filePath": "_home/channelManage.tsx",
      "parent": "/_home"
    },
    "/_home/channelManageDetail": {
      "filePath": "_home/channelManageDetail.tsx",
      "parent": "/_home"
    },
    "/_home/clientUser": {
      "filePath": "_home/clientUser.tsx",
      "parent": "/_home"
    },
    "/_home/computeCenter": {
      "filePath": "_home/computeCenter.tsx",
      "parent": "/_home"
    },
    "/_home/computeDemand": {
      "filePath": "_home/computeDemand.tsx",
      "parent": "/_home"
    },
    "/_home/customizedAICharacter": {
      "filePath": "_home/customizedAICharacter.tsx",
      "parent": "/_home"
    },
    "/_home/data": {
      "filePath": "_home/data.tsx",
      "parent": "/_home"
    },
    "/_home/dataWhitelist": {
      "filePath": "_home/dataWhitelist.tsx",
      "parent": "/_home"
    },
    "/_home/distManage": {
      "filePath": "_home/distManage.tsx",
      "parent": "/_home"
    },
    "/_home/exchangePackage": {
      "filePath": "_home/exchangePackage.tsx",
      "parent": "/_home"
    },
    "/_home/fastClone": {
      "filePath": "_home/fastClone.tsx",
      "parent": "/_home"
    },
    "/_home/figure": {
      "filePath": "_home/figure.ts",
      "parent": "/_home"
    },
    "/_home/generateVideo": {
      "filePath": "_home/generateVideo.tsx",
      "parent": "/_home"
    },
    "/_home/online-statistics": {
      "filePath": "_home/online-statistics.tsx",
      "parent": "/_home"
    },
    "/_home/order": {
      "filePath": "_home/order.tsx",
      "parent": "/_home"
    },
    "/_home/performance-analysis": {
      "filePath": "_home/performance-analysis.tsx",
      "parent": "/_home"
    },
    "/_home/platformAccount": {
      "filePath": "_home/platformAccount.tsx",
      "parent": "/_home"
    },
    "/_home/setting": {
      "filePath": "_home/setting.tsx",
      "parent": "/_home"
    },
    "/_home/soundClone": {
      "filePath": "_home/soundClone.tsx",
      "parent": "/_home"
    },
    "/_home/systemNotify": {
      "filePath": "_home/systemNotify.tsx",
      "parent": "/_home"
    },
    "/_home/team": {
      "filePath": "_home/team.tsx",
      "parent": "/_home"
    },
    "/_home/updateScript": {
      "filePath": "_home/updateScript.tsx",
      "parent": "/_home"
    },
    "/_home/userOrder": {
      "filePath": "_home/userOrder.tsx",
      "parent": "/_home"
    },
    "/_home/version": {
      "filePath": "_home/version.tsx",
      "parent": "/_home"
    },
    "/_home/voice": {
      "filePath": "_home/voice.ts",
      "parent": "/_home"
    }
  }
}
ROUTE_MANIFEST_END */
