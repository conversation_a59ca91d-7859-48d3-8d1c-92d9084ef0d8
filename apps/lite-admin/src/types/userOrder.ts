// 订单基础信息
export interface OrderBase {
  orderId: string;
  orderNo: string;
  userId: string;
  userName: string;
  userPhone?: string;
  teamName?: string;
  productId: string;
  productName: string;
  quantity: number;
  amount: number;
  payWay: string;
  status: number;
  attachmentUrl?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderType {
  _id: string;
  attachmentUrl: string;
  createdAt: string; // ISO 8601格式日期字符串
  description: string;
  expirationAt: string; // ISO 8601格式日期字符串
  isRefund: boolean;
  operateAdminId: string;
  operateAdminName: string;
  orderHashrate: number;
  orderSn: string;
  orderStatus: number;
  payAt: string; // ISO 8601格式日期字符串
  payStatus: number;
  payWay: number;
  price: number;
  productName: string;
  refundStatus: null | number; // 可能为null或具体状态码
  residueHashrate: number;
  teamCode: string;
  teamId: string;
  teamName: string;
  updatedAt: string; // ISO 8601格式日期字符串
  userId: null | string; // 可能为null或用户ID
}
export type OrderTypeList = {
  data: OrderType[],
  totalSize?:number,
  totalPage?:number
}

// 订单列表项
export interface OrderItem extends OrderBase {

}

// 订单详情
export interface OrderDetail extends OrderBase {

}

// 订单列表响应
export interface OrderListResponse {
  data: OrderItem[];
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
}

// 订单详情响应
export interface OrderDetailResponse {
  data: OrderDetail;
}

// 创建订单请求参数
export interface CreateOrderParams {
  productId: string;
  teamCode?: string;
  attachmentUrl?: string;
  description?: string;
}

// 更新订单请求参数
export interface UpdateOrderParams {
  orderId: string;
  attachmentUrl?: string;
  description?: string;
}

// 退款订单请求参数
export interface RefundOrderParams {
  orderId: string;
}

// 获取订单列表请求参数
export interface FetchOrderParams {
  page?: number;
  size?: number;
  searchType?: number;
  searchText?: string;
  productName?: string;
  payWay?: string;
  appKey?: string;
  orderStatus?: number;
  startDate?: number;
  endDate?: number;
}

// 获取订单详情请求参数
export interface GetOrderParams {
  orderId: string;
}