export type AdDto = {
  id: string;
  name: string;
  adminName?: string;
  sort: number;
  adUrl: string;
  adPath?: string;
  enabled: boolean;
  isTimed: boolean;
  expiredStartAt: number;
  expiredEndAt: number;
  isJumpTo: boolean;
  jumpToUrl: string;
};

export class AdListInput {
  name?: string;
  enabled?: string;
}

export type AddAdConfigInput = {
  id?: string;
  name: string;
  sort: number;
  adUrl?: string;
  enabled: boolean;
  isTimed: boolean;
  isJumpTo: boolean;
  expiredStartAt?: number;
  expiredEndAt?: number;
  jumpToUrl?: string;
};
