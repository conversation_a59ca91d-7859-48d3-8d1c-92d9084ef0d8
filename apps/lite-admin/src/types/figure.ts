export type FetchFigureParams = {
  page?:number,
  size?:number,
  search?: string,
  searchText?: string,
  searchType?: string,
  listType: number,
  startDate?: number,
  endDate?: number,
  type?: string,
}

export interface FigureItem {
  originalVideoUrl: string | undefined;
  _id: string;
  teamId?: string,
  teamName?: string,
  teamCode?: string,
  userId?: string,
  userAvatar?: string,
  userNickName?: string,
  userPhone?: string,
  type?: string,
  virtualmanId?: string,
  name?: string,
  cover?: string,
  listType: number,
  figureId?: string,
  defaultVoiceId?: string,
  reason?: string,
  demoUrl?: string,
  createdAt?: string,
  updatedAt?: string,
  status?: string
  authVideoUrl?: string,
}

export interface FigureResponse {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: FigureItem[];
}