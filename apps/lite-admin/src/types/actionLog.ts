// 操作日志项
export interface OperationLogItem {
  _id: string;
  action: string;                  // 操作类型，如"添加代理商"
  admin: {                        // 操作人信息
    _id: string;
    username: string;
    name: string;
  };
  appKey: string;                 // 应用标识
  content: string;                // 操作内容
  createdAt: string;              // 创建时间
  creatorAdminId: string;         // 创建者ID
  module: string;                 // 操作模块，如"设置"
  updatedAt: string;              // 更新时间
}

// 操作日志列表响应
export interface OperationLogListResponse {
  data: OperationLogItem[];
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
}

// 获取操作日志请求参数
export interface FetchOperationLogParams {
  page?: number;
  size?: number;
  searchType?: number;            // 搜索类型：1=手机号，2=操作人
  searchText?: string;            // 搜索内容
  action?: string;                // 操作类型
  startDate?: number;             // 开始时间(时间戳)
  endDate?: number;               // 结束时间(时间戳)
  module?:string
}