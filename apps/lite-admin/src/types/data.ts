export type WorkData = {
  date: string;
  count: number;
};

export type UserData = {
  date: string;
  count: number;
};

export type DataStatistic = {
  date: string;
  registerCount: number;
  appUserPublishCount: number;
  teamPublishCount: number;
  publishCount: number;
};

export type DataScale = {
  videoPublishNum: number;
  textPublishNum: number;
  articlePublishNum: number;
  oneClickPublishNum: number;
  browserPublishNum: number;
  appPublishNum: number;
};

/**
 * RegisterScaleDTO
 */
export interface RegisterScaleRes {
  /**
   * 安卓注册数
   */
  androidRegisterNum: number;
  /**
   * ios注册数
   */
  iosRegisterNum: number;
  /**
   * mac注册数
   */
  macRegisterNum: number;
  /**
   * windows注册数
   */
  windowsRegisterNum: number;
}

export interface AccountScaleRes {
  /**
   * 开始时间
   */
  platformName: string;
  /**
   * 结束时间
   */
  count: number;
}

export interface OnlineScaleRes {
  /**
   * 平台名称
   */
  platformName: string;
  /**
   * 数据对象
   */
  stats: { [key: string]: number };
}
