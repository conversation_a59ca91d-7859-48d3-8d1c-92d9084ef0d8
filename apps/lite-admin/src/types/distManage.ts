
// 渠道商响应体
export type DistManageResponse = {
  _id: string,
  adminCount: number,
  appKey: string,
  appName: string,
  createdAt: string,
  updatedAt: string,
  description: string,
  hashrate: number,
  isPlatform: boolean,
  status: number,
}

// 响应数据类型定义
export type DistManageDetailResponse = {
  data: DistManageResponse[];   // 渠道商列表
  page: number;            // 当前页码
  size: number;            // 每页数量
  totalSize: number;       // 总数据条数
  totalPage: number;       // 总页数
}


// 添加代理商
export type AddDistManage = {
  appName: string,   //代理商名字
  description?: string,  //用户备注
}

// 修改代理商
export type UpdateDistManageParams = {
  id: string,
  appName: string,
  description: string,
  status: number
}

// 分配算力
export type AllocateDistManageParams = {
  id:string,   //被分配的代理商ID
  hashrate:string,  // 分配的算力
}