// 添加商品类型的基础结构
export interface ProductBase {
  productName: string;
  hashrate: number;
  price: number;
}

export interface ProductBaseRequest {
  _id:string,
  productName: string;
  hashrate: number;
  price: number;
  createdAt:string,
  updatedAt:string
}

// 更新商品
export interface UpdateProductRequest {
  _id: string;           // 商品ID
  productName?: string; // 商品名称（可选）
  hashrate?: number;    // 算力（可选）
  price?: number;       // 价格（可选）
}


// 删除商品
export interface DeleteProductRequest {
  _id: string;
}