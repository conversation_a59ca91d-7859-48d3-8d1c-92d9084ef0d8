// 主index频道数据+频道设置page
export type channelDetailList = {

  _id?: string,
  channelCode?: string,
  channelName?: string,
  coverUrl?: string | null,
  description?: string | null,
  peopleNum?: number | null,
  consumeHashrate?: number | null,
  channelType?: string,
  impower?: string,
  isRecommend?: string,
  createdAt?: number | null,
  updatedAt?: number | null,
  status?: number | string | null;

}

export type FetchChannelParams = {
  searchType?: number;
  searchText?: string;
  startDate?: string | null;
  endDate?: string | null;
};
//-----------end-------------

// 分类管理page
export type ClassCategory = {
  _id: string;
  appKey: string;
  channelId: string;
  type: 1 | 2; // 1 = 文案, 2 = 素材
  name: string;
  status: 0 | 1; // 0 = 未启用, 1 = 启用
  sort: number;
  createdAt: string;
  updatedAt: string;
}

// 分类频道创建 类型
export interface FetchCategoryParams {
  searchType: number; // 1=名称
  searchText?: string;
  startDate?: number;
  endDate?: number;
  channelId: string;
  page?: number;
  pageSize?: number;
}

export interface CategoryItem {
  _id: string;
  channelId: string;
  type: 1 | 2; // 1=文案 2=素材
  name: string;
  status?: 1 | 2; // 默认下架2
  sort?: number; // 排序字段
  createdAt?: string;
  updatedAt?: string;
}

export interface GetCategoryList {
  channelId: string;
  type?: 1 | 2; // 可选筛选条件
  page?: number;
  pageSize?: number;
}

export interface CategoryListResponse {
  data: CategoryItem[];
  total: number;
  page: number;
  pageSize: number;
}

export type DeleteCategoryParams = {
  _id?: string;
  id?: string
}
export type Delete = {
  id?: string
}

// update
export interface UpdateCategoryParams {
  _id: string;
  channelId: string;
  type: 1 | 2;
  name: string;
  status: 1 | 2;
  sort?: number;
}
//-----------------end---------------------

// 内容管理page
// 添加素材
export type CreatePost = {
  channelId: string;
  isMulti: boolean;
  files: { url: string; postfix: string }[];//因为这里可以单个上传 可以批量上传
  categoryId: string;
}

// 获取素材库返回的数据项
interface MaterialItem {
  _id: string;
  appKey: string;
  channelId: string;
  categoryId: string;
  fileUrl: string;
  postfix: string;
  status: 0 | 1;
  sort: number;
  createdAt: string;
  updatedAt: string;
}

// 获取素材库返回的整体结构
export type GetMaterialList = {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: MaterialItem[];
}
// 修改素材
export type UpdateMaterial = {
  ids: string[];         // 素材 IDs（数组）
  categoryId: string;    // 分类 ID
}
// ----------------------end-------------

// 内容管理page
// 添加文案
export type AddText = {
  channelId: string;       // 频道ID
  isMulti: boolean;        // 是否多图上传方式
  categoryId?: string;     // 分类ID（可选）
  title: string;           // 标题
  content: string;         // 内容正文
  materialIds: string[];   // 绑定的素材IDs数组
  fileUrl?: string;        // 文件URL（可选）
};

export type TextItemList = {
  _id: string;
  appKey: string;
  channelId: string;
  categoryId: string;
  title: string;
  content: string;
  materialIds: string[];
  status: 0 | 1;            // 状态：0未启用，1已启用
  sort: number;             // 排序字段
  createdAt: string;        // 创建时间（ISO格式）
  updatedAt: string;        // 更新时间（ISO格式）
};

export type FetchTexts = {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: TextItemList[];
};

// 修改文案
export type UpdateTexts = {
  ids: string[];             // 文案 ID 列表（支持批量修改）
  categoryId?: string;       // 分类 ID（可选字段）
  title?: string;            // 标题（可选字段）
  content?: string;          // 内容（可选字段）
  contentIds?: string[];     // 绑定的素材 IDs（可选字段）
}

// 两者皆删除类型
export type DeleteItemsMT = {
  ids: string[];
};

// ----------------------end-------------
// 人员管理分区
// 一：增加频道成员api----
export type AddMember = {
  channelId: string;
  userId?: string;
  phone?: string;  // id和手机号互选其一
}

// 查询参数（获取成员列表）
export type FetchMembersParams = {
  page?: number;                // 当前页码，默认 1
  size?: number;                // 每页数量，默认 10
  channelId: string;            // 频道ID（必填）
  searchType?: number;        // 搜索类型：1=用户编号，2=手机号，3=昵称
  searchText?: string;          // 搜索内容
  status?: '0' | '1';           // 审核状态：0=待审核，1=已通过
  startDate?: number;
  endDate?: number;
};

// 用户数据项
export type ChannelMemberItem = {
  _id: string;
  id: string;
  appKey: string;
  phone: string;
  avatar: string;
  nickName: string;
  latestTeamId: string;
  customerId: string;
  channelCode: string;
  registrationSource: string;
  createdAt: string;
  updatedAt: string;
  status?:number;
  authVideoUrl: string;
};

// 分页数据结构
export type PageData<T> = {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: T[];
};

// 响应结构
export type FetchChannelMembersResponse = {
  page: number,
  size: number,
  totalSize: number,
  totalPage: number,
  data: ChannelMemberItem[];
};
// ---------分割线-----



// 频道运营 （管理员区）
/**
 * 创建后台用户 - 请求参数
 */
export interface CreateAdminUser {
  username?: string;         // 登录用户名
  name?: string;             // 用户姓名
  password?: string;         // 密码
  role?: number;             // 角色
  qrCode?: string;          // 可选二维码信息
  status?: number;           // 状态（例如：0-启用，1-禁用）
  szrChannelId?: string;     // 所属频道ID
  phone: string;            // 手机号

}

/**
 * 创建后台用户 - 响应数据
 */
export interface AdminUserList {
  id?: string,
  name?: string;
  username?: string,
  role?: number;
  qrCode?: string;
  createdAt?: number,
  status?: number,
  phone?: string,
  szrChannelId?: string
}

/**
 * 统一响应包装
 */
export interface AdminUserResponse {
  stateCode: number;         // 状态码，0 表示成功
  page: number,
  size: number,
  totalSize: number,
  totalPage: number,
  data: AdminUserList[];
}