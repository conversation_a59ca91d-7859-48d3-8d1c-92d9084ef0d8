// 配置项
export type fastCloneParams = {
  masterSwitch?: 0 | 1;
  masterSwitchValue?: boolean;
  topspeedSwitch?: 0 | 1;
  topspeedHashrate?: number;
  canTopspeedRange?: 1 | 2;
  searchType?: 1 | 2;
  searchText?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  size?: number;
};



// 用户信息类型get
export type userFastCloneInfo = {
  code?: string;
  _id?:string;
  phone?: string; 
  nickName?:string;
  createdAt?: string;
  updatedAt?: string;
  remarks?: string;
  del?: string | boolean; 
  totalSize?: number;
  salt?: string;
};
