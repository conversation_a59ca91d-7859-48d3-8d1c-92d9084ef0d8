export type CustomizedType = 'PRO' | 'FAST';

/** PRO: 专业版；FAST:极速版 */
export enum CustomizedTypeEnum {
  PRO = "PRO",
  FAST = "FAST",
}

export class CustomizedTypeMap {
  static PRO = "专业版";
  static FAST = "极速版";

  static getType(type: CustomizedType) {
    return CustomizedTypeMap[type];
  }
}

export interface UpdateCustomizedAICharacterInput {
  type?: string;
  proCourseVideoUrl?: string | null;
  proCourseImageUrl?: string | null;
  // courseText: string | null;
  proCourseText: string | null;
  topspeedCourseVideoUrl?: string | null;
  topspeedCourseImageUrl?: string | null;   
  videoFile?: File | null;
  imgFile?: File | null;
}