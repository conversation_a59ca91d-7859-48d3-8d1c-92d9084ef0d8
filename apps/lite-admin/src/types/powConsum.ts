export interface HashrateFormValues {
  genSzrHashrate: string;
  genVoiceHashrate: string;
  genVideoHashrate: string;
  genIntelligentVideoHashrate: string;
  genTopspeedVideoHashrate: string;
  copywritingExtractionHashrate: string;
  audioSamplePlaybackHashrate: string;
  dpskNarrationHashrate: string;
  narrationHashrate: string;
  copywritingRevisionHashrate: string;
  genTitleHashrate: string;
  genXHSHashrate: string;
  translateHashrate: string;
  wxMomentsHashrate: string;

  searchType?: number;
  startDate?: string;
  endDate?: string;
}

export interface PowerConsumptionItem {
  id: keyof HashrateFormValues; 
  name: string;
  unit: string;
  isHighSpeedSupported: boolean;
  highSpeedRate?: number;
}