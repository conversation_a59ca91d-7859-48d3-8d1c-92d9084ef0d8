export type FetchVideoParams = {
  search?: string,
  searchText?: string,
  searchType?: string,
  startDate?: number,
  endDate?: number,
}

export interface FetchChannelResponse {

}

export interface VideoCreationItem {
  _id: string;
  teamId: string;
  teamName: string;
  teamCode: string;
  userId: string;
  userAvatar: string;
  userNickName: string;
  userPhone: string;
  type: string;
  userCreationId: string;
  fileUrl: string;
  thumbnailUrl: string;
  duration: number; // in seconds
  expirationTime: string;
  creationType: string;
  text: string;
  title: string;
  virtualmanId: string;
  templateId: string;
  reason: string;
  createdAt: string;
  updatedAt: string;
  status:string;
}

export interface VideoCreationResponse {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: VideoCreationItem[];
}