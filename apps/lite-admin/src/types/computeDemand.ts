export interface TeamHashrateLogRequest {
  page?: number;
  size?: number;
  searchType: number;    // 搜索类型：1=团队编号，2=流水号
  searchText: string;
  startDate: string;
  endDate: string;
  changeType?:number;
}

export enum ETeamHashrateChangeType {
  ADD = 1,        // 增加算力
  DEDUCT = 2,     // 扣除算力
  TRANSFER_IN = 3,// 转入算力
  TRANSFER_OUT = 4// 转出算力
}

// 响应体
export interface TeamHashrateLogItem {
  _id: string;
  teamId: string;
  teamName: string;
  teamCode: string;
  userId: string;
  userAvatar: string;
  userNickName: string;
  userPhone: string;
  orderSns: string[];
  hashtype: 1 | 2;
  changeType: number;
  changeValue: number;
  beforeValue: number;
  afterValue: number;
  productName: string | null;
  description: string;
  createdAt: string;
  updatedAt: string;
}

// 总响应
export interface TeamHashrateLogResponse {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: TeamHashrateLogItem[];
}

