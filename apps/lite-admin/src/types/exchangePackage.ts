export interface Pagination {
  page?: number;
  size?: number;
}

// 获取兑换码包参数
export interface GetExchangePackagesParams extends Pagination {
  searchType: 1 | 2 | 3; // 1=兑换码 2=兑换码名称 3=创建人
  searchText: string;
}

// 管理员信息
export interface AdminInfo {
  _id: string;
  name: string;
}

// 单个兑换码包数据
export interface ExchangePackageItem {
  _id: string;            // 改为_id以匹配接口
  appKey: string;         // 新增字段
  packageName: string;
  productId: string;      // 改为string类型
  hashrate: number;       // 改为必填字段
  packageNum: number;
  usePackageNum: number;  // 新增字段
  creatorAdminId: string; // 新增字段
  createdAt: string;
  updatedAt: string;      // 新增字段
  admin: AdminInfo;       // 新增管理员信息
  status?:number,
}

// 分页响应数据（用这个）
export interface ExchangePackagePageData {
  page: number;
  size: number;
  totalSize: number;
  totalPage: number;
  data: ExchangePackageItem[];
}

// 完整响应体(可以忽略)
export interface GetExchangePackagesResponse {
  statusCode: number;
  data: ExchangePackagePageData;
}

// 添加兑换码包请求体
export interface AddExchangePackageBody {
  packageName: string;  // 兑换码名称
  productId: string;     // 算力包id
  packageNum: number;    // 购买数量>=1&&<=200
}

// 作废兑换码包
export interface InvalidExchangePackageBody {
  packageId: string;
}
// 获取兑换码详情 --请求体
export interface GetExchangePackageDetailParams extends Pagination {
  packageId: string;
  searchType: 1 | 2;
  searchText: string;
  status: number;
}
// 兑换码详情响应体
export interface ExchangeCodeItem {
  _id: string;
  appKey: string;
  code: string;
  createdAt: string; // ISO 8601格式
  exchangedAt: string | null;
  hashrate: number;
  orderSn: string | null;
  packageId: string;
  productId: string;
  status: number; // 1表示某种状态，可根据业务需求改为枚举
  updatedAt: string; // ISO 8601格式
  userId: string | null;
}

// 页面详情需要data
export interface RedeemCodeDetailResponse {
  statusCode?: number;
  data?: ExchangeCodeItem[]; // 根据图片，data是数组
  total?: number; // 可选的分页字段
}

// 作废兑换码包 都是post
export interface InvalidExchangePackage {
  packageId: string;
}
// 作废单个兑换码
export interface InvalidExchangeCode {
  codeId: string;
}