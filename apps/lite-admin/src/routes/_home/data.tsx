import { createFileRoute } from '@tanstack/react-router';
import { Card, CardContent, CardHeader, CardTitle } from '@mono/ui/card';
import { useQuery } from '@tanstack/react-query';
import { getDataScale, getDataStatistic } from '@/api/data';
import {
  CartesianGrid,
  LabelList,
  Line,
  LineChart,
  Pie,
  PieChart,
  XAxis,
  YAxis,
} from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@mono/ui/chart';
import { ScrollArea } from '@mono/ui/scroll-area';
import { RegisterScale } from '@/components/overviews/register';
import { AccountScale } from '@/components/overviews/account';

export const Route = createFileRoute('/_home/data')({
  component: DataRoot,
});

const dataStatisticChartConfig = {
  publishCount: {
    label: '作品发布数',
    color: 'hsl(var(--chart-1))',
  },
  teamPublishCount: {
    label: '活跃发布团队数',
    color: 'hsl(var(--chart-3))',
  },
  registerCount: {
    label: '新增用户数',
    color: 'hsl(var(--chart-2))',
  },
  appUserPublishCount: {
    label: 'APP发布用户数',
    color: 'hsl(var(--chart-5))',
  },
} satisfies ChartConfig;

const chartConfig = {
  visitors: {
    label: '数量',
  },
  articlePublishNum: {
    label: '文章',
    color: 'hsl(var(--chart-1))',
  },
  textPublishNum: {
    label: '图文',
    color: 'hsl(var(--chart-2))',
  },
  videoPublishNum: {
    label: '视频',
    color: 'hsl(var(--chart-3))',
  },
  oneClickPublishNum: {
    label: '一键发布',
    color: 'hsl(var(--chart-4))',
  },
  browserPublishNum: {
    label: '浏览器发布',
    color: 'hsl(var(--chart-5))',
  },
  appPublishNum: {
    label: 'APP发布',
    color: 'hsl(var(--chart-6))',
  },
} satisfies ChartConfig;

function DataRoot() {
  const dataStatisticQuery = useQuery({
    queryKey: ['dataStatistic'],
    queryFn: getDataStatistic,
  });

  const dataScaleQuery = useQuery({
    queryKey: ['dataScale'],
    queryFn: getDataScale,
  });

  const chartData = [];
  const channelChartData = [];

  if (
    dataScaleQuery.data?.articlePublishNum &&
    dataScaleQuery.data?.articlePublishNum > 0
  ) {
    chartData.push({
      browser: 'articlePublishNum',
      visitors: dataScaleQuery.data?.articlePublishNum,
      fill: 'var(--color-articlePublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.textPublishNum &&
    dataScaleQuery.data?.textPublishNum > 0
  ) {
    chartData.push({
      browser: 'textPublishNum',
      visitors: dataScaleQuery.data?.textPublishNum,
      fill: 'var(--color-textPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.videoPublishNum &&
    dataScaleQuery.data?.videoPublishNum > 0
  ) {
    chartData.push({
      browser: 'videoPublishNum',
      visitors: dataScaleQuery.data?.videoPublishNum,
      fill: 'var(--color-videoPublishNum)',
    });
  }

  if (
    dataScaleQuery.data?.oneClickPublishNum &&
    dataScaleQuery.data?.oneClickPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'oneClickPublishNum',
      visitors: dataScaleQuery.data?.oneClickPublishNum,
      fill: 'var(--color-oneClickPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.browserPublishNum &&
    dataScaleQuery.data?.browserPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'browserPublishNum',
      visitors: dataScaleQuery.data?.browserPublishNum,
      fill: 'var(--color-browserPublishNum)',
    });
  }
  if (
    dataScaleQuery.data?.appPublishNum &&
    dataScaleQuery.data?.appPublishNum > 0
  ) {
    channelChartData.push({
      browser: 'appPublishNum',
      visitors: dataScaleQuery.data?.appPublishNum,
      fill: 'var(--color-appPublishNum)',
    });
  }

  return (
    <ScrollArea className="flex flex-col overflow-hidden">
      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl">30日作品发布量</CardTitle>
            </CardHeader>

            <CardContent>
              <ChartContainer
                config={dataStatisticChartConfig}
                className="w-full h-80"
              >
                <LineChart
                  accessibilityLayer
                  data={dataStatisticQuery.data?.data ?? []}
                  margin={{
                    top: 12,
                    left: 12,
                    right: 30,
                    bottom: 30,
                  }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickLine={true}
                    axisLine={false}
                    angle={-45}
                    tickMargin={24}
                    tickFormatter={(value) => value}
                  />
                  <YAxis
                    dataKey="publishCount"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Line
                    dataKey="publishCount"
                    type="natural"
                    stroke="var(--color-publishCount)"
                    strokeWidth={2}
                    dot={{
                      fill: 'var(--color-publishCount)',
                    }}
                    activeDot={{
                      r: 6,
                    }}
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>

          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl">30日活跃发布团队数</CardTitle>
            </CardHeader>

            <CardContent>
              <ChartContainer
                config={dataStatisticChartConfig}
                className="w-full h-80"
              >
                <LineChart
                  accessibilityLayer
                  data={dataStatisticQuery.data?.data ?? []}
                  margin={{
                    top: 12,
                    left: 12,
                    right: 30,
                    bottom: 30,
                  }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickLine={true}
                    axisLine={false}
                    angle={-45}
                    tickMargin={24}
                    tickFormatter={(value) => value}
                  />
                  <YAxis
                    dataKey="teamPublishCount"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Line
                    dataKey="teamPublishCount"
                    type="natural"
                    stroke="var(--color-teamPublishCount)"
                    strokeWidth={2}
                    dot={{
                      fill: 'var(--color-teamPublishCount)',
                    }}
                    activeDot={{
                      r: 6,
                    }}
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>
        <div className="flex items-center gap-4">
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl">30日APP发布用户数</CardTitle>
            </CardHeader>

            <CardContent>
              <ChartContainer
                config={dataStatisticChartConfig}
                className="w-full h-80"
              >
                <LineChart
                  accessibilityLayer
                  data={dataStatisticQuery.data?.data ?? []}
                  margin={{
                    top: 12,
                    left: 12,
                    right: 30,
                    bottom: 30,
                  }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickLine={true}
                    axisLine={false}
                    angle={-45}
                    tickMargin={24}
                    tickFormatter={(value) => value}
                  />
                  <YAxis
                    dataKey="appUserPublishCount"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Line
                    dataKey="appUserPublishCount"
                    type="natural"
                    stroke="var(--color-appUserPublishCount)"
                    strokeWidth={2}
                    dot={{
                      fill: 'var(--color-appUserPublishCount)',
                    }}
                    activeDot={{
                      r: 6,
                    }}
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>

          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl">30日新增用户数</CardTitle>
            </CardHeader>

            <CardContent>
              <ChartContainer
                config={dataStatisticChartConfig}
                className="w-full h-80"
              >
                <LineChart
                  accessibilityLayer
                  data={dataStatisticQuery.data?.data ?? []}
                  margin={{
                    top: 12,
                    left: 12,
                    right: 30,
                    bottom: 30,
                  }}
                >
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey="date"
                    tickLine={true}
                    axisLine={false}
                    angle={-45}
                    tickMargin={24}
                    tickFormatter={(value) => value}
                  />
                  <YAxis
                    dataKey="registerCount"
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                  />
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Line
                    dataKey="registerCount"
                    type="natural"
                    stroke="var(--color-registerCount)"
                    strokeWidth={2}
                    dot={{
                      fill: 'var(--color-registerCount)',
                    }}
                    activeDot={{
                      r: 6,
                    }}
                  />
                </LineChart>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-4">
          <div className="flex-1 min-w-[300px] ">
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="text-xl">发布类型占比</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="w-full">
                  <PieChart>
                    <ChartTooltip
                      content={
                        <ChartTooltipContent nameKey="visitors" hideLabel />
                      }
                    />
                    <Pie data={chartData} dataKey="visitors">
                      <LabelList
                        dataKey="browser"
                        className="fill-background"
                        stroke="none"
                        fontSize={12}
                        formatter={(value: keyof typeof chartConfig) =>
                          chartConfig[value]?.label
                        }
                      />
                    </Pie>
                  </PieChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
          <div className="flex-1 min-w-[300px] ">
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="text-xl">发布渠道占比</CardTitle>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="w-full">
                  <PieChart>
                    <ChartTooltip
                      content={
                        <ChartTooltipContent nameKey="visitors" hideLabel />
                      }
                    />
                    <Pie data={channelChartData} dataKey="visitors">
                      <LabelList
                        dataKey="browser"
                        className="fill-background"
                        stroke="none"
                        fontSize={12}
                        formatter={(value: keyof typeof chartConfig) =>
                          chartConfig[value]?.label
                        }
                      />
                    </Pie>
                  </PieChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </div>
        <div className="flex flex-wrap justify-center items-center gap-4">
          <div className="flex-1 min-w-[300px]">
            <RegisterScale />
          </div>
          <div className="flex-1 min-w-[300px]">
            <AccountScale />
          </div>
        </div>
      </div>
    </ScrollArea>
  );
}
