import { createFileRoute, redirect, Outlet } from '@tanstack/react-router';
import { useUserStore } from '@/store/user';
import { AppSidebar } from '@/components/layout/nav';
import { SidebarProvider, SidebarInset } from '@mono/ui/sidebar';

export const Route = createFileRoute('/_home')({
  beforeLoad: ({ location }) => {
    if (!useUserStore.getState().userAuthorization) {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: MainContentLayout,
});

function MainContentLayout() {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <main className="flex-1 h-full p-3 overflow-hidden">
          <Outlet />
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
