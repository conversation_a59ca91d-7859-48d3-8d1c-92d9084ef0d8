// /**
//  * 获取团队列表
//  * GET /teams
//  */
// export const teamList = (params: PageQuery & TeamListInput) => {
//   return makeRequest<PageResult<Team>>({
//     url: '/teams',
//     method: 'GET',
//     params,
//   });
// };

import { DataWhitelist, queryParams } from '@/types/dataWhitelist.ts';
import { makeRequest } from '@/api/index.ts';
import { PageResult } from '@/types';

export const getList = (params: queryParams) => {
  return makeRequest<PageResult<DataWhitelist>>({
    url: `/teams/cloud-data-list`,
    method: 'GET',
    params,
  });
};

export const addTeam = (params: Array<{ id: string; enabled: boolean }>) => {
  return makeRequest<null>({
    url: `/teams/cloud-data`,
    method: 'PUT',
    data: params,
  });
};

export const closeTeam = (params: Array<{ id: string; enabled: boolean }>) => {
  return makeRequest<null>({
    url: `/teams/cloud-data`,
    method: 'PUT',
    data: params,
  });
};
