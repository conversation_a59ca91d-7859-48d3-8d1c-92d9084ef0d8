import OSS from 'ali-oss';
import { getStsCredentials } from './channelManage';

async function uploadFileToOSS(file: File): Promise<string> {
  const credentials = await getStsCredentials();
  const client = new OSS({
    region: credentials.region,
    accessKeyId: credentials.accessKeyId,
    accessKeySecret: credentials.accessKeySecret,
    stsToken: credentials.securityToken,
    bucket: credentials.bucket,
    secure: true,
    refreshSTSToken: async () => {
      const creds = await getStsCredentials();
      return {
        accessKeyId: creds.accessKeyId,
        accessKeySecret: creds.accessKeySecret,
        stsToken: creds.securityToken,
      };
    },
    refreshSTSTokenInterval: 300000,
  });

  const env = import.meta.env.VITE_APP_NODE_ENV || 'dev';
  const fileExt = file.name.split('.').pop() || 'file';
  const filePath = `${env}/material/${Date.now()}_${Math.random().toString(36).slice(2)}.${fileExt}`;

  const result = await client.put(filePath, file);
  return result.url; // 返回 OSS 上的访问 URL
}

export default uploadFileToOSS