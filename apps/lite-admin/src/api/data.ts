import { makeRequest } from './index';
import { PageResult } from '@/types/index';
import {
  AccountScaleRes,
  DataScale,
  DataStatistic,
  OnlineScaleRes,
  RegisterScaleRes,
} from '@/types/data';

/**
 * 获取30日统计数据
 * GET /overviews/data-statistic
 */
export const getDataStatistic = () => {
  return makeRequest<PageResult<DataStatistic>>({
    url: '/overviews/data-statistic',
    method: 'GET',
  });
};

export const getDataScale = () => {
  return makeRequest<DataScale>({
    url: '/overviews/data-scale',
    method: 'GET',
  });
};

// 用户注册设备数据
//   GET /overviews/register-scale
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getRegisterScale = (dataType?: 'thirty') => {
  return makeRequest<RegisterScaleRes>({
    url: '/overviews/register-scale',
    method: 'GET',
    params: { dataType },
  });
};

// 媒体账号占比数据
//   GET /overviews/account-scale
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getAccountScale = () => {
  return makeRequest<AccountScaleRes[]>({
    url: '/overviews/account-scale',
    method: 'GET',
  });
};

// 账号登录保持统计
//   GET /overviews/online-scale
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getOnlineScale = () => {
  return makeRequest<OnlineScaleRes[]>({
    url: '/overviews/online-scale',
    method: 'GET',
  });
};
