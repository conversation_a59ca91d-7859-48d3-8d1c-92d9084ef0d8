import { makeRequest } from '@/api/index.ts';
import { PageCountQuery, PageResult } from '@/types';
import { Account, AccountReq, ServiceAccount } from '@/types/admins.ts';

export const getAdminList = (params: PageCountQuery) => {
  return makeRequest<PageResult<Account>>({
    url: '/users',
    method: 'GET',
    params,
  });
};

export const getAdminById = (id: string) => {
  return makeRequest<Account>({
    url: `/users/${id}`,
    method: 'GET',
  });
};

export const updateAdmin = (
  id: string,
  params: Omit<AccountReq, 'password'>
) => {
  return makeRequest<Account>({
    url: `/users/${id}`,
    method: 'PATCH',
    data: params,
  });
};

export const deleteAdmin = (id: string) => {
  return makeRequest<Account>({
    url: `/users/${id}`,
    method: 'DELETE',
  });
};

export const createAdmin = (params: AccountReq) => {
  return makeRequest<Account>({
    url: `/users`,
    method: 'POST',
    data: params,
  });
};

// 更新用户状态
//   PUT /users/{id}/status
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const updateUserStatus = ({
  id,
  status,
}: {
  id: string;
  status: number;
}) => {
  return makeRequest({
    url: `/users/${id}/status`,
    method: 'PUT',
    data: { status },
  });
};

// 重置密码
//   PUT /users/{id}/password
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const resetPassword = ({
  id,
  password,
}: {
  id: string;
  password: string;
}) => {
  return makeRequest({
    url: `/users/${id}/password`,
    method: 'PUT',
    data: { password },
  });
};

// 获取客服列表
//   GET /users/customers
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getCustomerList = () => {
  return makeRequest<Array<ServiceAccount>>({
    url: `/users/customers`,
    method: 'GET',
    headers: {
      appkey: 'X2aP0FNKjzG3XZ1zKIaGKk4Jv9WTqaNA', 
    },
  });
};
