import { makeRequest } from './index';
import { FetchFigureParams, FigureResponse } from '@/types/figure';

export const fetchFigure = (params: FetchFigureParams) => {
  return makeRequest<FigureResponse>({
    url: '/szr/resource/fetchFigure',
    method: 'GET',
    params,
  })
}

// 获取声音
export const fetchVoice = (params: FetchFigureParams) => {
  return makeRequest<FigureResponse>({
    url: '/szr/resource/fetchVoice',
    method: 'GET',
    params,
  })
}