import { makeRequest } from './index';
import {
  OrderDetailResponse,
  CreateOrderParams,
  UpdateOrderParams,
  RefundOrderParams,
  FetchOrderParams,
  GetOrderParams,
  OrderType,
  OrderTypeList,
} from '@/types/userOrder';


const baseURL = '/szr/trading/'



// 获取订单列表
export const fetchOrderList = (params: FetchOrderParams) => {
  console.log(params);
  
  return makeRequest<OrderTypeList>({
    url: `${baseURL}fetchOrder`,
    method: 'GET',
    params,
  });
};

// 获取订单详情
export const getOrderDetail = (params: GetOrderParams) => {
  const orderId = params

  return makeRequest<OrderType>({
    url: `${baseURL}getOrder`,
    method: 'GET',
    params: orderId,
  });
};

// 创建订单
export const createOrder = (data: CreateOrderParams) => {
  return makeRequest<OrderDetailResponse>({
    url: `${baseURL}createOrder`,
    method: 'POST',
    data,
  });
};

// 更新订单
export const updateOrder = (data: UpdateOrderParams) => {
  return makeRequest<OrderDetailResponse>({
    url: `${baseURL}updateOrder`,
    method: 'POST',
    data,
  });
};

// 退款订单
export const refundOrder = (data: RefundOrderParams) => {
  return makeRequest({
    url: `${baseURL}refundOrder`,
    method: 'POST',
    data,
  });
};