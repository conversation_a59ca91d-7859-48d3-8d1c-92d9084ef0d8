import { makeRequest } from './index';
import { PageData, } from '@/types/index';
import {
  userFastCloneInfo,
  fastCloneParams
} from '@/types/fastClone';
// import { Phone } from 'lucide-react';

export const fastCloneList = (params: fastCloneParams) => {
  const processedParams = {
    ...params,
    startDate: params.startDate ? new Date(params.startDate).getTime() : undefined,
    endDate: params.endDate
      ? (() => {
        const date = new Date(params.endDate);
        date.setHours(23, 59, 59, 999);
        return date.getTime();
      })()
      : undefined,
    page: params.page ?? params.page ? params.page + 1 : 1, 
    size: params.size ?? params.size ?? 10,
    canTopspeedRange: params.canTopspeedRange ? params.canTopspeedRange : 1,
  };
  return makeRequest<PageData<userFastCloneInfo>>({
    url: '/settings/fetchTopspeedUsers',
    method: 'GET',
    params: processedParams,
  });
};


// 删除
export const removeFastCloneList = ({ _id }: { _id?: string }) => {
console.log(_id);

  return makeRequest({
    url: '/settings/removeTopspeedUser',
    method: 'POST',
    data: {
      userId:_id
    }
  });
};


// 添加用户x
export const addFastCloneList = (data: userFastCloneInfo) => {
  const addUser = {
    phone: data.phone ? data.phone : undefined,
  }
  console.log(typeof (addUser.phone));

  return makeRequest({
    url: '/settings/saveTopspeedUser',
    method: 'POST',
    headers:{
      appkey: 'X2aP0FNKjzG3XZ1zKIaGKk4Jv9WTqaNA',
    },
    data: {
      phone: String(data.phone),
    }
  });
};


// 获取极速克隆配置项fin
export const fastClonEConfigurationItem = (
  params: Omit<fastCloneParams, 'searchType'> & { searchType?: number } = {}
) => {

  return makeRequest<fastCloneParams>({
    // return makeRequest<PageData<fastCloneParams>>({
    url: '/settings/fetchFastCloneSettings',
    method: 'GET',
    params: {
      ...params,
    },
  });
};



// 保存极速克隆配置项fin
export const fastClonePreserve = async (data: fastCloneParams) => {
  console.log(data);
  const switchTopSpeed = {
    topspeedSwitch: !!data.masterSwitchValue,
    topspeedHashrate: data.topspeedHashrate,
    canTopspeedRange: data.canTopspeedRange
  }
  return makeRequest<fastCloneParams>({
    url: '/settings/save',
    method: 'POST',
    data: switchTopSpeed
  });
};