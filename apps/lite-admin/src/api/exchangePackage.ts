import { makeRequest } from './index';
import {
  GetExchangePackagesParams,
  // GetExchangePackagesResponse,
  AddExchangePackageBody,
  GetExchangePackageDetailParams,
  ExchangePackagePageData,
  RedeemCodeDetailResponse,
  InvalidExchangePackage,
  InvalidExchangeCode
} from '../types/exchangePackage';


const baseURL = '/szr/trading/'

// --------------------------
// 🔹 获取兑换码包 - GET over
// --------------------------
export const fetchExchangePackages = (
  params: GetExchangePackagesParams
) => {
  return makeRequest<ExchangePackagePageData>({
    url: `${baseURL}fetchExchangePackage`,
    method: 'GET',
    params,
  });
}

// --------------------------
// 🔹 添加兑换码包 - POST over
// --------------------------
export const addExchangePackage = (
  data: AddExchangePackageBody
) => {

  return makeRequest<{ success: boolean }>({
    url: `${baseURL}addExchangePackage`,
    method: 'POST',
    data,
  });
}

// --------------------------
// 🔹 获取兑换码包详情 - GET  
// --------------------------
export const fetchExchangePackageDetail = (
  params: GetExchangePackageDetailParams
) => {
  return makeRequest<RedeemCodeDetailResponse>({
    url: `${baseURL}fetchExchangePackageDetail`,
    method: 'GET',
    params,
  });
}

// --------------------------
// 🔹 作废兑换码包 - POST 
// --------------------------
export const invalidExchangePackage = (
  data: InvalidExchangePackage
) => {
  return makeRequest<{ success: boolean }>({
    url: `${baseURL}invalidExchangePackage`,
    method: 'POST',
    data,
  });
}

// --------------------------
// 🔹 作废单个兑换码 - POST 
// --------------------------
export const invalidExchangeCode = (
  data: InvalidExchangeCode
) => {
  return makeRequest<{ success: boolean }>({
    url: `${baseURL}invalidExchangeCode`,
    method: 'POST',
    data,
  });
}
