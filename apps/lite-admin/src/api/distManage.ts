import { AddDistManage, AllocateDistManageParams, DistManageDetailResponse, UpdateDistManageParams } from '@/types/distManage';
import { makeRequest } from './index';

// 代理商这个页面的接口是复古接口，id带在接口地址后面

const baseURL = '/agency/'

// --------------------------
// 🔹 代理商获取  - GET
// --------------------------
export const fetDistManageList = (
  params: any
) => {
  return makeRequest<DistManageDetailResponse>({
    url: `${baseURL}fetch`,
    method: 'GET',
    params,
  });
}


// --------------------------
// 🔹 添加代理商 - POST 
// --------------------------
export const addDistManage = (
  data: AddDistManage
) => {

  return makeRequest<{ success: boolean }>({
    url: `${baseURL}add`,
    method: 'POST',
    data,
  });
}


// --------------------------
// 🔹 修改代理商 - POST 
// --------------------------
export const updateDistManage = (
  data: UpdateDistManageParams
) => {

  return makeRequest<{ success: boolean }>({
    url: `${baseURL}update`,
    method: 'POST',
    data,
  });
}


// --------------------------
// 🔹 分配算力 - POST 
// --------------------------
export const allocateDistManage = (
  data: AllocateDistManageParams
) => {

  return makeRequest<{ success: boolean }>({
    url: `/agency/allocationHashrate`,
    method: 'POST',
    data,
  });
}





