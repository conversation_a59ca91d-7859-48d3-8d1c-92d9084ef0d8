import { ProductBase } from '@/types/powPackage';
import { makeRequest } from '../index';
import { HashrateFormValues } from '@/types/powConsum';

const baseURL = '/settings/';

// 获取算力设置
export const fetchHashrateConfig = (params: ProductBase) => {
  return makeRequest<HashrateFormValues>({
    url: `${baseURL}fetchHashrateConfig`,
    method: 'GET',
    params,
  });
};

// 保存配置项
export const saveConfiguration = (data: HashrateFormValues) => {
  return makeRequest({
    url: `${baseURL}save`,
    method: 'POST',
    data: data,
  });
};