import { makeRequest } from '../index';

const baseURL = '/settings/'

interface FetchTorialCenterParams {

}

export interface FetchTorialCenterResponse {
  _id?: string;
  appKey?: string,
  title?: string;
  videoUrl?: string;
  coverImageUrl?: string;
  voiceSort?: string;
  createdAt?: string;
  updatedAt?: string;
  length?:number;
}

// 获取教程中心的视频
export const fetchCourseVideoList = (params?: FetchTorialCenterParams): Promise<FetchTorialCenterResponse> => {
  return makeRequest({
    url: `${baseURL}fetchCourseVideo`,
    method: 'GET',
    params,
  });
};

// 新增
export const addCourseVideoList = (data: FetchTorialCenterResponse) => {

  return makeRequest({
    url: `${baseURL}addCourseVideo`,
    method: 'POST',
    data,
  });
};

// 修改
export const updateCourseVideoList = (data: FetchTorialCenterResponse) => {
  const dataList = {
    ...data,
    id: data._id
  }
  return makeRequest({
    url: `${baseURL}updateCourseVideo`,
    method: 'POST',
    data: dataList
  });
};


// 删除
export const deleteCourseVideo = (id: string) => {
  return makeRequest({
    url: `${baseURL}deleteCourseVideo`,
    method: 'POST',
    data:{id}
  });
};