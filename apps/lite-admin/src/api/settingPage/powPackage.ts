import { DeleteProductRequest, ProductBase, ProductBaseRequest, UpdateProductRequest } from '@/types/powPackage';
import { makeRequest } from '../index';

const baseURL = '/szr/product/'

// 获取算力产品
export const fetchPowConsumList = (params: ProductBase) => {
  return makeRequest<ProductBaseRequest[]>({
    url: `${baseURL}fetch`,
    method: 'GET',
    params,
  });
};

// 创建算力产品
export const addPowConsum = (data: ProductBase) => {
  return makeRequest<ProductBaseRequest[]>({
    url: `${baseURL}create`,
    method: 'POST',
    data
  });
};

// 更新算力产品(修改) 
export const repairPowConsum = (data: UpdateProductRequest) => {
  console.log(data);
  const dataAll = {
    hashrate: data.hashrate,
    price: data.price,
    productName: data.productName,
    id: data._id
  }
  return makeRequest<UpdateProductRequest[]>({
    url: `${baseURL}update`,
    method: 'POST',
    data: dataAll
  });
};

// 删除算力产品
export const delPowConsum = ({ _id }: DeleteProductRequest) => {
  const id = _id

  return makeRequest<DeleteProductRequest[]>({
    url: `${baseURL}delete`,
    method: 'POST',
    data: {id}
  });
};

