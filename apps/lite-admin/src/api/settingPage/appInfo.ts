import { makeRequest } from '../index';

const baseURL = '/settings/';


export interface AppInfoResponse {
  serviceQrCodeUrl: string;
  serviceTips: string;
  appletShareTips: string;
  WebVideoDownloadSwitch: boolean;
  appletShareCardUrl: string;
  guidanceCourseSwitch: boolean;
  guidanceCourseVideoUrl: string;
  statusCode: number;
}

interface FetchAppInfoParams {

}

export const fetchAppInfo = (params?: FetchAppInfoParams): Promise<AppInfoResponse> => {
  return makeRequest({
    url: `${baseURL}fetchAppInfo`,
    method: 'GET',
    params,
  });
};

export const saveConfiguration = (data: AppInfoResponse) => {
  return makeRequest({
    url: `${baseURL}save`,
    method: 'POST',
    data: data,
  });
};