import { makeRequest } from './index';
import { PageData } from '@/types/index';
import {
  Member,
  MemberReq,
  UserLoginInput,
  UserLoginOutput,
  UserPermissionSettingInput,
  UserTwoFactorReq,
} from '@/types/user';

/**
 * 用户登录
 * POST /users/auth
 */
export const userAuth = (params: UserLoginInput) => {
  return makeRequest<UserLoginOutput>({
    url: '/users/auth',
    method: 'POST',
    headers: {
      appkey: 'X2aP0FNKjzG3XZ1zKIaGKk4Jv9WTqaNA', // ←←← 正确添加 appkey 请求头
    },
    data: params,
  });
};

/**
 * 退出登录
 * DELETE /users/auth
 */
export const userLoginOut = () => {
  return makeRequest<object>({
    url: '/users/auth',
    method: 'DELETE',
  });
};

/**
 * 获取用户列表
 * GET /members
 */
export const memberList = (params: MemberReq) => {
  return makeRequest<PageData<Member>>({
    url: '/members',
    method: 'GET',
    params,
  });
};

/**
 * 设置用户信息
 * PATCH /members/{id}/config
 */
export const setPermission = (input: UserPermissionSettingInput) => {
  return makeRequest<object>({
    url: `/members/${input.id}/config`,
    method: 'PATCH',
    data: {
      state: input.state,
      accountCountLimit: input.accountCountLimit,
    },
  });
};

// 更换客服归属人
//   PATCH /members/{id}/customer
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const changeCustomer = ({
  id,
  customerId,
}: {
  id: string;
  customerId: string;
}) => {
  return makeRequest({
    url: `/members/${id}/customer`,
    method: 'PATCH',
    data: {
      customerId,
    },
  });
};

// 获取跟进列表
//   GET /follow-records/{followUserId}
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const getFollowRecords = (followUserId: string) => {
  return makeRequest<
    {
      id: string;
      customerName: string;
      content: string;
      createdAt: string;
    }[]
  >({
    url: `/follow-records/${followUserId}`,
    method: 'GET',
  });
};

// 添加跟进内容
//   POST /follow-records/{followUserId}
//   接口ID：*********
//   接口地址：https://app.apifox.com/link/project/5710261/apis/api-*********
export const addFollowRecord = ({
  followUserId,
  content,
}: {
  followUserId: string;
  content: string;
}) => {
  return makeRequest({
    url: `/follow-records/${followUserId}`,
    method: 'POST',
    data: {
      content,
    },
  });
};

// 二次验证mfa
// POST /users/two/factor/auth
// 接口ID：256642204
// 接口地址：https://app.apifox.com/link/project/4645177/apis/api-256642204
export const userTwoFactorAuth = (params: UserTwoFactorReq) => {
  return makeRequest<UserLoginOutput>({
    url: '/users/two/factor/auth',
    method: 'POST',
    data: params,
  });
};
