import { makeRequest } from './index';
import {
  channelDetailList,
  FetchChannelParams,
  GetMaterialList,
  CreatePost,
  CategoryItem,
  FetchCategoryParams,
  DeleteCategoryParams,
  UpdateCategoryParams,
  AddText,
  DeleteItemsMT,
  FetchTexts,
  AddMember,
  FetchMembersParams,
  FetchChannelMembersResponse,
  CreateAdminUser,
  AdminUserList,
  AdminUserResponse,
  Delete,
  UpdateMaterial,
  UpdateTexts
} from '@/types/channelManage';
import OSS from 'ali-oss';
// import { method } from 'lodash';

const baseUrl = "/szr/channel/"

// 获取频道
export const fetchChannelList = (params: FetchChannelParams) => {
  return makeRequest<channelDetailList[]>({
    url: `${baseUrl}fetch`,
    method: 'GET',
    params,
  });
};

// 创建频道
export const addChannelCom = (params: channelDetailList) => {
  return makeRequest<channelDetailList[]>({
    url: `${baseUrl}fastCreate`,
    method: 'POST',
    data: params,
  });
};

// 删除频道
export const delChannel = ({ _id }: channelDetailList) => {
  return makeRequest<channelDetailList[]>({
    url: `${baseUrl}del`,
    method: 'POST',
    data: {
      id: _id
    },
  });
};



// -------------oss上传channeluRL-------------

/**
 * 上传频道封面图片到 OSS
 * @param file 图片文件（File 对象或已上传的 URL）
 * @param onProgress 上传进度回调
 * @returns 返回完整的 OSS URL
 */
export const getStsCredentials = async () => {
  return makeRequest<{
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucket: string;

  }>({
    url: '/oss/sts',
    method: 'GET',
    headers: {
      appkey: 'X2aP0FNKjzG3XZ1zKIaGKk4Jv9WTqaNA'
    }
  });
};

export const saveChannelWithCover = async (
  params: {
    _id: string;
    channelName: string;
    description: string;
    coverFile?: File | null; // 新封面文件（可选）
    currentCoverUrl?: string; // 现有封面URL（如果有）
    // 其他必填字段
    channelType: string;
    impower: string;
    isRecommend: string | boolean;
    status: string;
  },
) => {
  // 1. 如果有新封面文件，先上传到OSS
  let finalCoverUrl = params.currentCoverUrl || '';
  if (params.coverFile) {
    try {
      const credentials = await getStsCredentials();
      const client = new OSS({
        region: credentials.region,
        accessKeyId: credentials.accessKeyId,
        accessKeySecret: credentials.accessKeySecret,
        stsToken: credentials.securityToken,
        bucket: credentials.bucket,
        secure: true,
        refreshSTSToken: async () => {
          const creds = await getStsCredentials();
          return {
            accessKeyId: creds.accessKeyId,
            accessKeySecret: creds.accessKeySecret,
            stsToken: creds.securityToken,
          };
        },
        refreshSTSTokenInterval: 300000,
      });
      const env = import.meta.env.VITE_APP_NODE_ENV || 'dev';
      const filePath = `${env}/channel/covers/${params._id}_${Date.now()}_${params.coverFile.name}`;
      const result = await client.put(filePath, params.coverFile,);
      finalCoverUrl = result.url;
    } catch (error) {
      console.error('封面图片上传失败:', error);
      throw new Error('封面图片上传失败，请重试');
    }
  }

  // 2. 构造后端需要的payload（严格匹配接口字段）
  const payload = {
    id: params._id,
    channelName: params.channelName,
    coverUrl: finalCoverUrl,
    description: params.description,
    channelType: params.channelType,
    impower: params.impower,
    isRecommend: params.isRecommend,
    status: params.status
  };

  // 3. 调用频道更新API
  return makeRequest<channelDetailList>({
    url: `${baseUrl}complete`,
    method: 'POST',
    data: payload,
  });
};

/**
 * 仅更新频道基本信息（不处理封面）
 */
export const completeChannel = (params: channelDetailList) => {
  return makeRequest<channelDetailList>({
    url: `${baseUrl}complete`,
    method: 'POST',
    data: {
      id: params._id,
      channelName: params.channelName,
      coverUrl: params.coverUrl || '',
      description: params.description || '',
      channelType: params.channelType || '1',
      impower: params.impower || '1',
      isRecommend: params.isRecommend || 'false',
      status: String(params.status ?? '1')
    }
  });
};



// --------------分类管理API~classificationManage----------------
/**
 * 新建分类
 * @param /szr/channel/createContentGategory
 *@param"channelId": "频道ID",
        "type": "type：1=文案 2=素材",
        "name": "分类名称",
        "status": "上下架状态",
  @param"sort": "排序字段"
 */
export const createContentGategory = (params: {
  channelId: string;
  type: 1 | 2;
  name: string;
  status?: 1 | 2;
  sort?: number;
}) => {
  console.log(params);

  return makeRequest<CategoryItem>({
    url: `${baseUrl}createContentGategory`,
    method: 'POST',
    data: {
      ...params,
      status: params.status || 2, // 默认下架  1上架 2下架
      sort: params.sort || 0
    },
  });
};

/**
 * 获取分类列表
 * @param /szr/channel/fetchContentGategory
 */
export const fetchContentGategory = (params: FetchCategoryParams) => {
  console.log(params);

  return makeRequest<CategoryItem[]>({
    url: `${baseUrl}fetchContentGategory`,
    method: 'GET',
    params
  });
};

// 删除分类  一般来说只要id就可以,但这里
export const delContentGategory = ({ _id }: DeleteCategoryParams) => {
  return makeRequest<DeleteCategoryParams[]>({
    url: `${baseUrl}delContentGategory`,
    method: 'POST',
    data: {
      id: _id
    },
  });
};

/**
 * 更新分类（名称/状态）
 */
export const updateContentGategory = (params: UpdateCategoryParams) => {
  return makeRequest<CategoryItem>({
    url: `${baseUrl}updateContentGategory`,
    method: 'POST',
    data: {
      id: params._id,//分类id
      channelId: params.channelId,//频道id
      type: params.type,//文案or素材
      name: params.name,//分类名字
      status: params.status,//上架or下架
      sort: params.sort || 0,
    }
  });
};


// --------------内容管理API~contentManage----------------
/**
 * 添加素材   同频的还有个添加文案,添加文案可以以素材的文件为素材,也可以没有
 * @param /szr/channel/addMaterial/
 * 先添加
 */
export const addMaterial = (params: CreatePost) => {

  return makeRequest<CreatePost[]>({
    url: `${baseUrl}addMaterial`,
    method: 'POST',
    data: params,
  });
};

/**
 * 获取素材
 * @param /szr/channel/fetchMaterial
 * 然后是获取
 */
export const fetchMaterial = (params: GetMaterialList) => {
  return makeRequest<GetMaterialList>({
    url: `${baseUrl}fetchMaterial`,
    method: 'GET',
    params,
  });
};

/**
 * 删除素材
 * @param /szr/channel/delMaterial
 */
export const delMaterial = (params: DeleteItemsMT) => {
  return makeRequest({
    url: `${baseUrl}delMaterial`,
    method: 'POST',
    data: params,
  });
};

/**
 * 修改素材
 * @param /szr/channel/updateMaterial
 */
export const updateMaterial = (params: UpdateMaterial) => {
  return makeRequest({
    url: `${baseUrl}updateMaterial`,
    method: 'POST',
    data: params,
  });
};
//-----------------------
/**
 * 添加文案  
 * @param  /szr/channel/addTexts
 * 先添加
 */
export const addTexts = (params: AddText) => {
  return makeRequest<AddText[]>({
    url: `${baseUrl}addTexts`,
    method: 'POST',
    data: params,
  });
};

/**
 * 获取文案
 * @param /szr/channel/fetchTexts
 * 然后是获取
 */
export const fetchTexts = (params: FetchTexts) => {
  return makeRequest<FetchTexts>({
    url: `${baseUrl}fetchTexts`,
    method: 'GET',
    params,
  });
};

/**
 * 修改文案
 * @param 
 */
export const updateTexts = (params: UpdateTexts) => {
  return makeRequest({
    url: `${baseUrl}updateTexts`,
    method: 'POST',
    data: params,
  });
};
/**
 * 删除文案
 * @param /szr/channel/delTexts
 */
export const delTexts = (params: DeleteItemsMT) => {
  return makeRequest({
    url: `${baseUrl}delTexts`,
    method: 'POST',
    data: params,
  });
};

// ---------------end-----------
// 频道用户左边，频道成员
// 新增频道成员（非管理员）
export const addMembers = (params: AddMember) => {
  return makeRequest<AddMember[]>({
    url: `${baseUrl}addMembers`,
    method: 'POST',
    data: params,
  });
};

// 获取频道用户列表
export const fetchMembers = (params: FetchMembersParams) => {
  return makeRequest<FetchChannelMembersResponse>({
    url: `${baseUrl}fetchMembers`,
    method: 'GET',
    params,
  });
};

// 移除频道用户
export const removeMembers = (id: DeleteCategoryParams) => {//移除只需要id即可
  return makeRequest({
    url: `${baseUrl}removeMembers`,
    method: 'POST',
    data: id,
  });
};

// ----------end-----------
// 创建管理员用户列表
export const createAdminUser = (params: CreateAdminUser) => {
  return makeRequest<CreateAdminUser[]>({
    url: `/users`,
    method: 'POST',
    data: params,
  });
};

// 获取管理员用户列表
export const fetchAdminUser = (params: AdminUserList) => {
  return makeRequest<AdminUserResponse>({
    url: `/users`,
    method: 'GET',
    params,
  });
};

// 删除管理员用户列表，传id
export const removeAdminUser = (id: Delete) => {
  const userId = id.id
  return makeRequest({
    url: `/users/${userId}`,
    method: 'DELETE',
  });
};
