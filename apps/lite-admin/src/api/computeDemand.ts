import { TeamHashrateLogRequest, TeamHashrateLogResponse } from '@/types/computeDemand';
import { makeRequest } from '.';

// 获取团队更改算力配置
export const fetchTeamHashrateLog = (params: TeamHashrateLogRequest) => {
  return makeRequest<TeamHashrateLogResponse>({
    url: '/hashrate/team/log',
    method: 'GET',
    params,
  });
};

// 获取算力中心更改配置
export const fetchCenterHashrateLog = (params: TeamHashrateLogRequest) => {
  return makeRequest<TeamHashrateLogResponse>({
    url: '/hashrate/platform/log',
    method: 'GET',
    params,
  });
};