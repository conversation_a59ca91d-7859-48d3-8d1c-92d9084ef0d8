import { makeRequest } from './index';
import {
  soundCloneParams,
} from '@/types/soundClone';
import OSS from 'ali-oss';
// import { method } from 'lodash';


// 获取极速克隆配置项fin
export const fetchVoiceTemp = (params: soundCloneParams) => {

  return makeRequest<soundCloneParams>({
    url: '/settings/fetchVoiceTemp',
    method: 'GET',
    params: {
      ...params,
    },
  });
};

// 保存声音克隆配置项fin
export const saveVoiceTemp = async (data: soundCloneParams[]) => {
  const payload = { temps: data };
  return makeRequest({
    url: '/settings/saveVoiceTemp',
    method: 'POST',
    data: payload,
  });
};
// ---------------
export const saveVoiceTempw = async () => {
  return makeRequest<{
    region: string;
    accessKeyId: string;
    accessKeySecret: string;
    securityToken: string;
    bucket: string;

  }>({
    url: '/oss/sts',
    method: 'GET',
    headers: {
      appkey: 'X2aP0FNKjzG3XZ1zKIaGKk4Jv9WTqaNA'
    }
  });
};

// 保存配置项只能在这里保存oss.ts
export const saveVoiceTemps = async (allData: soundCloneParams[]) => {
  // 1. 获取OSS凭证
  const credentials = await saveVoiceTempw();
  
  // 2. 初始化OSS客户端
  const client = new OSS({
    region: credentials.region,
    accessKeyId: credentials.accessKeyId,
    accessKeySecret: credentials.accessKeySecret,
    stsToken: credentials.securityToken,
    bucket: credentials.bucket,
    secure: true,
    refreshSTSToken: async () => {
      const creds = await saveVoiceTempw();
      return {
        accessKeyId: creds.accessKeyId,
        accessKeySecret: creds.accessKeySecret,
        stsToken: creds.securityToken,
      };
    },
    refreshSTSTokenInterval: 300000,
  });

  const env = import.meta.env.VITE_APP_NODE_ENV;
  
  // 3. 并行处理所有需要上传/删除的音频文件
  const updatedData = await Promise.all(allData.map(async (item) => {
    // 没有待处理的文件，直接返回
    if (item.pendingFile === undefined) {
      return item;
    }

    // 处理删除操作
    if (item.pendingFile === null) {
      return { ...item, voiceUrl: '', pendingFile: undefined };
    }

    // 处理上传操作
    try {
      const fileExtension = item.pendingFile.name.split('.').pop();
      const voicePath = `${env}/cus_ai_chara/100001/voice/${item._id}_${Date.now()}.${fileExtension}`;
      
      // 上传文件到OSS
      const result = await client.put(voicePath, item.pendingFile);//路径 文件
      
      // 获取文件URL
      const url = result.url;
      return { 
        ...item, 
        voiceUrl: url,
        pendingFile: undefined // 清除临时文件
      };
    } catch (error) {
      console.error('上传失败:', error);
      return item; // 上传失败保持原样
    }
  }));

  // 4. 过滤出需要保存到后端的数据（去除pendingFile字段）
  const dataToSave = updatedData.map(({ pendingFile, ...rest }) => rest);//rest新对象，map组成数组，结构为[{}]

  // 5. 将数据保存到后端
  return makeRequest({
    url: '/settings/saveVoiceTemp',
    method: 'POST',
    data: { temps: dataToSave }
  });
};






