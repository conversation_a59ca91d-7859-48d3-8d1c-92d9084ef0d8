stages:
  - triggers

.shared-rules: &shared-rules
  - changes:
      - shared/**/*

trigger-lite-admin:
  stage: triggers
  trigger:
    include: apps/lite-admin/.gitlab-ci.yml
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "pre-production" || $CI_COMMIT_BRANCH == "production"
      changes:
        - apps/lite-admin/**/*
    - *shared-rules

trigger-qdy-admin:
  stage: triggers
  trigger:
    include: apps/qdy-admin/.gitlab-ci.yml
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "pre-production" || $CI_COMMIT_BRANCH == "production"
      changes:
        - apps/qdy-admin/**/*
    - *shared-rules
