import dayjs, { ManipulateType } from 'dayjs';

import quarterOfYear from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import zh from 'dayjs/locale/zh-cn';
dayjs.extend(quarterOfYear);
dayjs.extend(localizedFormat);
dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.locale(zh);

export const fromNow = (time: number) => {
  // 判断是 time 是秒还是毫秒时间戳
  if (time.toString().length === 10) {
    return dayjs(time * 1000).fromNow();
  }
  return dayjs(time).fromNow();
};

export const formatBy = (time: Parameters<typeof dayjs>[0], format: string) => {
  return dayjs(time).format(format);
};

export const formatDate = (time: Parameters<typeof dayjs>[0]) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 格式化年月日
export const formatYearMonthDay = (time: Parameters<typeof dayjs>[0]) => {
  return dayjs(time).format('YYYY/MM/DD');
};

// 格式化月日
export const formatMonthDay = (time: Parameters<typeof dayjs>[0]) => {
  return dayjs(time).format('MM-DD');
};

// 获取当前时间戳
export const getTimestamp = () => {
  return dayjs().valueOf();
};

// 获取时长毫秒
export const getDuration = dayjs.duration;

export function formatTimestamp(timestamp: Parameters<typeof dayjs>[0]) {
  const now = dayjs();
  const date = dayjs(timestamp);
  const isSameDay = now.isSame(date, 'day');
  const isYesterday = now.subtract(1, 'day').isSame(date, 'day');

  if (isSameDay) {
    return date.format('HH:mm');
  } else if (isYesterday) {
    return `昨天 ${date.format('HH:mm')}`;
  } else {
    return date.format('M月DD日 HH:mm');
  }
}

export function isExpiredFun(expiresTime: Parameters<typeof dayjs>[0]) {
  return dayjs(expiresTime).valueOf() - getTimestamp() <= 0;
}

// 增加时长
export function addDuration(
  duration: number,
  unit: ManipulateType,
  date?: Parameters<typeof dayjs>[0]
) {
  return dayjs(date).add(duration, unit).valueOf();
}

// 减少时长
export function reduceDuration(
  duration: number,
  unit: ManipulateType,
  date?: Parameters<typeof dayjs>[0]
) {
  return dayjs(date).subtract(duration, unit).valueOf();
}

//格式化时长
export function formatDuration(duration: number, template = 'mm:ss') {
  return dayjs.duration(duration, 'milliseconds').format(template);
}

/**
 * 判断给定时间是否在当前时间往前x小时内（包含往前x小时的时间）
 * @param time 给定时间
 * @param x 多少小时
 * @returns
 */
export function isWithinLastXHours(
  time: Parameters<typeof dayjs>[0],
  x: number
) {
  // 获取当前时间
  const now = dayjs();
  // 计算 x 小时前的时间
  const xHoursAgo = now.subtract(x, 'hour');

  // 比较给定时间是否在x小时前之后
  return dayjs(time).isSame(xHoursAgo) || dayjs(time).isAfter(xHoursAgo);
}

// 根据时间获取时间戳
export function getTimestampByTime(time: Parameters<typeof dayjs>[0]) {
  return dayjs(time).valueOf();
}

// 获取某一天的结束时间 23, 59, 59, 999
export function getDayEndTime(time: Parameters<typeof dayjs>[0]) {
  return dayjs(time).endOf('day').valueOf();
}
